"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { ReportsList } from "./reports-list";
import { ReportTemplates } from "./report-templates";
import { ReportScheduler } from "./report-scheduler";
import { CustomReportBuilder } from "./custom-report-builder";
import { ReportMetrics } from "./report-metrics";
import { ReportHeader } from "./report-header";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Sparkles, TrendingUp, Clock, Zap, Info } from "lucide-react";

export default function ReportsDashboard() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <DashboardLayout>
      <div className="flex min-h-screen w-full flex-col bg-gradient-to-br from-background via-background to-muted/20">
        <ReportHeader activeTab={activeTab} />

        <main className="flex-1 space-y-8 p-6 md:p-8">
          {/* Enhanced welcome section */}
          <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-teal-500/10 dark:from-blue-500/5 dark:via-purple-500/5 dark:to-teal-500/5 p-6 backdrop-blur-sm border border-blue-200/20 dark:border-blue-800/20">
            <div className="relative z-10">
              <div className="flex items-center gap-3 mb-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
                  <TrendingUp className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Business Intelligence Dashboard
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Generate insights, analyze trends, and export professional reports
                  </p>
                </div>
              </div>
              
              <Alert className="bg-blue-50/50 dark:bg-blue-950/30 border-blue-200/50 dark:border-blue-800/30">
                <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <AlertDescription className="text-blue-800 dark:text-blue-300">
                  <strong>Data Source:</strong> Reports analyze data based on when invoices were added to the system, not their issue date.
                </AlertDescription>
              </Alert>
            </div>
            
            {/* Background decoration */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute top-4 right-4 h-32 w-32 rotate-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-600" />
              <div className="absolute bottom-4 left-4 h-24 w-24 -rotate-12 rounded-full bg-gradient-to-br from-teal-400 to-blue-600" />
            </div>
          </div>
          
          <ReportMetrics />

          <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-muted/30 dark:from-background dark:to-muted/10">
            <CardContent className="p-6">
              <Tabs
                defaultValue="overview"
                className="space-y-6"
                onValueChange={setActiveTab}
              >
                <div className="flex items-center justify-between">
                  <TabsList className="grid grid-cols-4 w-full max-w-md bg-background/80 dark:bg-background/20 backdrop-blur-sm border border-border/50">
                    <TabsTrigger 
                      value="overview" 
                      className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md dark:data-[state=active]:bg-primary/90 dark:data-[state=active]:text-primary-foreground transition-all duration-200"
                    >
                      <div className="flex items-center gap-2">
                        <Sparkles className="h-4 w-4" />
                        <span className="hidden sm:inline">Overview</span>
                      </div>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="templates"
                      className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md dark:data-[state=active]:bg-primary/90 dark:data-[state=active]:text-primary-foreground transition-all duration-200"
                    >
                      <div className="flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        <span className="hidden sm:inline">Templates</span>
                      </div>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="scheduled"
                      className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md dark:data-[state=active]:bg-primary/90 dark:data-[state=active]:text-primary-foreground transition-all duration-200"
                    >
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span className="hidden sm:inline">Scheduled</span>
                      </div>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="custom"
                      className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md dark:data-[state=active]:bg-primary/90 dark:data-[state=active]:text-primary-foreground transition-all duration-200"
                    >
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4" />
                        <span className="hidden sm:inline">Custom</span>
                      </div>
                    </TabsTrigger>
                  </TabsList>
                  
                  <Badge variant="secondary" className="hidden md:flex items-center gap-1 bg-background/80 dark:bg-background/20 border-border/50">
                    <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
                    <span className="text-foreground">Production Ready</span>
                  </Badge>
                </div>

                <TabsContent value="overview" className="space-y-6 animate-in fade-in-50 duration-300">
                  <ReportsList />
                </TabsContent>

                <TabsContent value="templates" className="space-y-6 animate-in fade-in-50 duration-300">
                  <ReportTemplates />
                </TabsContent>

                <TabsContent value="scheduled" className="space-y-6 animate-in fade-in-50 duration-300">
                  <ReportScheduler />
                </TabsContent>

                <TabsContent value="custom" className="space-y-6 animate-in fade-in-50 duration-300">
                  <CustomReportBuilder />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </main>
      </div>
    </DashboardLayout>
  );
}
