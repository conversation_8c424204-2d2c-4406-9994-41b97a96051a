"use server"

import { auth } from "@clerk/nextjs/server"
import db from "@/db/db"
import { put } from "@vercel/blob"
import { nanoid } from "nanoid"
import { generateInvoicePDF } from "@/lib/generators/invoice-generator"
import { generateInvoiceExcel } from "@/lib/generators/invoice-generator"

// Get all export folders for the current user
export async function getExportFolders() {
  try {
    const { userId } = await auth()

    if (!userId) {
      return { success: false, error: "Unauthorized" }
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Get all folders with invoice count
    const folders = await db.exportFolder.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        updatedAt: "desc",
      },
    })

    return {
      success: true,
      folders,
    }
  } catch (error) {
    console.error("Error fetching export folders:", error)
    return {
      success: false,
      error: "Failed to fetch export folders",
    }
  }
}

// Create a new export folder
export async function createExportFolder(name: string) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return { success: false, error: "Unauthorized" }
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Create the folder
    const folder = await db.exportFolder.create({
      data: {
        name,
        userId: user.id,
      },
    })

    return {
      success: true,
      folder,
    }
  } catch (error) {
    console.error("Error creating export folder:", error)
    return {
      success: false,
      error: "Failed to create export folder",
    }
  }
}

// Delete an export folder
export async function deleteExportFolder(folderId: string) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return { success: false, error: "Unauthorized" }
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Delete the folder
    await db.exportFolder.delete({
      where: {
        id: folderId,
        userId: user.id,
      },
    })

    return {
      success: true,
    }
  } catch (error) {
    console.error("Error deleting export folder:", error)
    return {
      success: false,
      error: "Failed to delete export folder",
    }
  }
}

// Rename an export folder
export async function renameExportFolder(folderId: string, newName: string) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return { success: false, error: "Unauthorized" }
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Update the folder name
    await db.exportFolder.update({
      where: {
        id: folderId,
        userId: user.id,
      },
      data: {
        name: newName,
      },
    })

    return {
      success: true,
    }
  } catch (error) {
    console.error("Error renaming export folder:", error)
    return {
      success: false,
      error: "Failed to rename export folder",
    }
  }
}

// Get exports for a specific folder
export async function getFolderExports(folderId: string) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return { success: false, error: "Unauthorized" }
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Get all exports for the folder
    const exports = await db.exportHistory.findMany({
      where: {
        folderId,
        userId: user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return {
      success: true,
      exports,
    }
  } catch (error) {
    console.error("Error fetching folder exports:", error)
    return {
      success: false,
      error: "Failed to fetch folder exports",
    }
  }
}

// Get invoices in a specific folder
export async function getFolderInvoices(folderId: string) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return { success: false, error: "Unauthorized" }
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Get all invoice IDs in the folder
    const folderInvoices = await db.folderInvoice.findMany({
      where: {
        folderId,
      },
      select: {
        invoiceId: true,
      },
    })

    const invoiceIds = folderInvoices.map(item => item.invoiceId)

    // Get the invoice details
    const invoices = await db.invoice.findMany({
      where: {
        id: { in: invoiceIds },
        userId: user.id,
      },
      select: {
        id: true,
        invoiceNumber: true,
        status: true,
        issueDate: true,
        dueDate: true,
        amount: true,
        currency: true,
        category: {
          select: {
            id: true,
            name: true,
            color: true,
          },
        },
        vendorName: true,
        vendor: {
          select: {
            id: true,
            name: true,
          },
        },
        thumbnailUrl: true,
        originalFileUrl: true,
        notes: true,
        extractedData: true,
        lineItems: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return {
      success: true,
      invoices,
    }
  } catch (error) {
    console.error("Error fetching folder invoices:", error)
    return {
      success: false,
      error: "Failed to fetch folder invoices",
    }
  }
}

// Remove an invoice from a folder
export async function removeInvoiceFromFolder(folderId: string, invoiceId: string) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return { success: false, error: "Unauthorized" }
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Get the current folder to check count
    const folder = await db.exportFolder.findUnique({
      where: {
        id: folderId,
        userId: user.id,
      },
      select: {
        invoiceCount: true,
      },
    })

    if (!folder) {
      return { success: false, error: "Folder not found" }
    }

    // Check if the relation exists
    const relation = await db.folderInvoice.findUnique({
      where: {
        folderId_invoiceId: {
          folderId,
          invoiceId,
        },
      },
    })

    if (!relation) {
      return { success: false, error: "Invoice not found in folder" }
    }

    // Remove the invoice from the folder
    await db.folderInvoice.delete({
      where: {
        folderId_invoiceId: {
          folderId,
          invoiceId,
        },
      },
    })

    // Update the folder's invoice count, ensuring it doesn't go below 0
    const newCount = Math.max(0, folder.invoiceCount - 1)
    
    await db.exportFolder.update({
      where: {
        id: folderId,
      },
      data: {
        invoiceCount: newCount,
        updatedAt: new Date(),
      },
    })

    return {
      success: true,
    }
  } catch (error) {
    console.error("Error removing invoice from folder:", error)
    return {
      success: false,
      error: "Failed to remove invoice from folder",
    }
  }
}

// Update folder with new invoices
export async function updateFolderWithInvoices(folderId: string, invoiceIds: string[]) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return { success: false, error: "Unauthorized" }
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Get the folder
    const folder = await db.exportFolder.findUnique({
      where: {
        id: folderId,
        userId: user.id,
      },
    })

    if (!folder) {
      return { success: false, error: "Folder not found" }
    }

    // Get existing invoice relationships
    const existingRelations = await db.folderInvoice.findMany({
      where: {
        folderId,
        invoiceId: { in: invoiceIds },
      },
      select: {
        invoiceId: true,
      },
    })

    const existingInvoiceIds = existingRelations.map(rel => rel.invoiceId)
    
    // Filter out invoices that are already in the folder
    const newInvoiceIds = invoiceIds.filter(id => !existingInvoiceIds.includes(id))
    
    if (newInvoiceIds.length === 0) {
      return {
        success: true,
        addedCount: 0,
      }
    }

    // Verify all invoices exist and belong to the user
    const validInvoices = await db.invoice.findMany({
      where: {
        id: { in: newInvoiceIds },
        userId: user.id,
      },
      select: {
        id: true,
      },
    })

    const validInvoiceIds = validInvoices.map(invoice => invoice.id)

    // Use createMany with skipDuplicates to avoid unique constraint errors
    const createResult = await db.folderInvoice.createMany({
      data: validInvoiceIds.map(invoiceId => ({
        folderId,
        invoiceId,
      })),
      skipDuplicates: true, // This will skip any records that would cause a unique constraint violation
    })

    const addedCount = createResult.count

    // Only update the folder count if new invoices were added
    if (addedCount > 0) {
      // Update the folder's invoice count and last updated time
      await db.exportFolder.update({
        where: {
          id: folderId,
        },
        data: {
          invoiceCount: {
            increment: addedCount,
          },
          updatedAt: new Date(),
        },
      })
    }

    return {
      success: true,
      addedCount,
    }
  } catch (error) {
    console.error("Error updating folder with invoices:", error)
    return {
      success: false,
      error: "Failed to update folder with invoices",
    }
  }
}

// Export invoices to a folder
export async function exportInvoices({
  invoiceIds,
  folderId,
  format,
  fields,
}: {
  invoiceIds: string[]
  folderId: string
  format: string
  fields: string[]
}) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return { success: false, error: "Unauthorized" }
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Get the folder
    const folder = await db.exportFolder.findUnique({
      where: {
        id: folderId,
        userId: user.id,
      },
    })

    if (!folder) {
      return { success: false, error: "Folder not found" }
    }

    // Get the invoices
    const invoices = await db.invoice.findMany({
      where: {
        id: { in: invoiceIds },
        userId: user.id,
      },
      include: {
        category: true,
        vendor: true,
        lineItems: true,
      },
    })

    if (invoices.length === 0) {
      return { success: false, error: "No invoices found" }
    }

    // Generate a unique export ID
    const exportId = nanoid()

    // Generate the file based on the format
    let fileBuffer: Buffer
    let contentType: string
    let fileExtension: string

    // Map database invoice structure to the expected type
    const mappedInvoices = invoices.map((invoice) => ({
      ...invoice,
      category: invoice.category
        ? {
            name: invoice.category.name,
            color: invoice.category.color || undefined,
          }
        : null,
      lineItems: invoice.lineItems.map((item) => ({
        ...item,
        attributes: item.attributes ? (item.attributes as Record<string, unknown>) : undefined,
      })),
    }))

    if (format === "pdf") {
      fileBuffer = await generateInvoicePDF(mappedInvoices.map(inv => ({
        ...inv,
        extractedData: inv.extractedData as Record<string, unknown> | undefined
      })), {})
      contentType = "application/pdf"
      fileExtension = "pdf"
    } else {
      fileBuffer = await generateInvoiceExcel(mappedInvoices.map(inv => ({
        ...inv,
        extractedData: inv.extractedData as Record<string, unknown> | undefined
      })), {})
      contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      fileExtension = "xlsx"
    }

    // Create a filename
    const fileName = `invoices_export_${new Date().toISOString().split("T")[0]}_${exportId}.${fileExtension}`

    // Upload the file to Vercel Blob
    const { url } = await put(`exports/${fileName}`, fileBuffer, {
      contentType,
      access: "public",
    })

    // Create an export history record
    const exportRecord = await db.exportHistory.create({
      data: {
        exportId,
        fileName,
        fileUrl: url,
        format,
        count: invoices.length,
        folderName: folder.name,
        folderId: folder.id,
        userId: user.id,
      },
    })

    // Get existing invoice relationships to avoid duplicates
    const existingRelations = await db.folderInvoice.findMany({
      where: {
        folderId,
        invoiceId: { in: invoiceIds },
      },
      select: {
        invoiceId: true,
      },
    })

    const existingInvoiceIds = existingRelations.map(rel => rel.invoiceId)
    
    // Filter out invoices that are already in the folder
    const newInvoiceIds = invoiceIds.filter(id => !existingInvoiceIds.includes(id))
    
    // Add new invoices to the folder using createMany with skipDuplicates
    if (newInvoiceIds.length > 0) {
      await db.folderInvoice.createMany({
        data: newInvoiceIds.map(invoiceId => ({
          folderId,
          invoiceId,
        })),
        skipDuplicates: true,
      })
      
      // Update the folder's invoice count with the actual count from the database
      const currentFolderInvoices = await db.folderInvoice.count({
        where: { folderId }
      })
      
      await db.exportFolder.update({
        where: {
          id: folderId,
        },
        data: {
          invoiceCount: currentFolderInvoices,
          updatedAt: new Date(),
        },
      })
    }

    return {
      success: true,
      downloadUrl: url,
      fileName,
      exportId: exportRecord.id,
    }
  } catch (error) {
    console.error("Error exporting invoices:", error)
    return {
      success: false,
      error: "Failed to export invoices",
    }
  }
}
