"use client";

import React, { useState, useEffect, useMemo, useRef, useLayoutEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  FileSpreadsheet,
  FileText,
  Loader2,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  X,
  Clock,
  ChevronDown,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { getReports, deleteReport, exportReport, generateReportData } from "@/lib/actions/reports";
import { formatDistanceToNow, format } from "date-fns";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import type { Report, ReportType } from "@prisma/client";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { addDays, startOfMonth, endOfMonth, startOfYear } from "date-fns";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { FixedSizeList as List, ListChildComponentProps } from 'react-window';

// Extended type to include potentially loaded relations
interface ReportWithRelations extends Omit<Report, 'title' | 'reportType'> {
  data?: Array<{
    id: string;
    reportId?: string | null;
    invoiceId?: string | null;
    dataPoint?: string | null;
    value?: number | null;
    label?: string | null;
    category?: string | null;
    createdAt?: Date;
  }>;
  scheduledReports?: Array<{ id: string }>;
  title?: string;
  reportType?: string;
  name?: string;
  type?: string;
}

type SortField = 'name' | 'type' | 'createdAt';
type SortDirection = 'asc' | 'desc';

// Debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debouncedValue;
}

export function ReportsList() {
  const [reports, setReports] = useState<ReportWithRelations[]>([]);
  const [filteredReports, setFilteredReports] = useState<ReportWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState<Record<string, boolean>>({});
  const router = useRouter();

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<ReportType | 'ALL'>('ALL');
  const [statusFilter, setStatusFilter] = useState<'ALL' | 'GENERATED' | 'DRAFT'>('ALL');
  const [scheduledFilter, setScheduledFilter] = useState<'ALL' | 'SCHEDULED' | 'NOT_SCHEDULED'>('ALL');
  const [dateRange, setDateRange] = useState<{ from: Date | undefined, to: Date | undefined }>({
    from: undefined,
    to: undefined,
  });
  const [sortField, setSortField] = useState<SortField>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  useEffect(() => {
    async function fetchReports() {
      try {
        const data = await getReports();
        setReports(data);
        setFilteredReports(data);
      } catch {
        toast.error("Failed to load reports");
      } finally {
        setLoading(false);
      }
    }

    fetchReports();
  }, []);

  // Apply filters whenever filter state changes
  useEffect(() => {
    let filtered = [...reports];

    // Apply search filter
    if (debouncedSearchQuery) {
      const query = debouncedSearchQuery.toLowerCase();
      filtered = filtered.filter(report => {
        const name = report.title || report.name || '';
        const type = report.reportType || report.type || '';
        return name.toLowerCase().includes(query) ||
          report.description?.toLowerCase().includes(query) ||
          type.toLowerCase().includes(query);
      });
    }

    // Apply type filter
    if (typeFilter !== 'ALL') {
      filtered = filtered.filter(report => {
        const type = report.reportType || report.type || '';
        return type === typeFilter;
      });
    }

    // Apply status filter
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(report => {
        const hasData = (report.data?.length ?? 0) > 0;
        if (statusFilter === 'GENERATED') return hasData;
        return !hasData;
      });
    }

    // Apply scheduled filter
    if (scheduledFilter !== 'ALL') {
      filtered = filtered.filter(report => {
        const isScheduled = (report.scheduledReports?.length ?? 0) > 0;
        if (scheduledFilter === 'SCHEDULED') return isScheduled;
        return !isScheduled;
      });
    }

    // Apply date range filter
    if (dateRange.from && dateRange.to) {
      filtered = filtered.filter(report => {
        const reportDate = new Date(report.createdAt);
        return reportDate >= dateRange.from! && reportDate <= dateRange.to!;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      if (sortField === 'name') {
        const aName = a.title || a.name || '';
        const bName = b.title || b.name || '';
        return sortDirection === 'asc'
          ? aName.localeCompare(bName)
          : bName.localeCompare(aName);
      } else if (sortField === 'type') {
        const aType = a.reportType || a.type || '';
        const bType = b.reportType || b.type || '';
        return sortDirection === 'asc'
          ? aType.localeCompare(bType)
          : bType.localeCompare(aType);
      } else {
        return sortDirection === 'asc'
          ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

    setFilteredReports(filtered);
  }, [reports, searchQuery, typeFilter, statusFilter, scheduledFilter, dateRange, sortField, sortDirection, debouncedSearchQuery]);

  // Memoize filteredReports for performance
  const memoizedFilteredReports = useMemo(() => filteredReports, [filteredReports]);

  // Track table width for virtualization
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [tableWidth, setTableWidth] = useState<number>(800);
  useLayoutEffect(() => {
    if (tableContainerRef.current) {
      setTableWidth(tableContainerRef.current.offsetWidth);
    }
  }, [memoizedFilteredReports.length]);

  // Only use virtualization for large lists
  const USE_VIRTUAL = memoizedFilteredReports.length > 20;

  // Memoize handler functions
  const handleDeleteReport = useCallback(async (id: string) => {
    try {
      await deleteReport(id);
      const updatedReports = reports.filter(report => report.id !== id);
      setReports(updatedReports);
      toast.success("Report deleted successfully");
    } catch {
      toast.error("Failed to delete report");
    }
  }, [reports]);

  const handleExportReport = useCallback(async (id: string, format: 'pdf' | 'excel') => {
    setExporting(prev => ({ ...prev, [id]: true }));

    // Create a unique ID for this export operation
    const operationId = `export-${id}-${format}-${Date.now()}`;

    try {
      // First, check if the report has data, if not, generate it
      const report = reports.find(r => r.id === id);

      if (!report?.data?.length) {
        // Update the toast message
        toast.loading("Generating report data first...", {
          id: operationId
        });

        try {
          // Generate report data
          await generateReportData(id);

          // Update toast message for export
          toast.loading(`Exporting report as ${format.toUpperCase()}...`, {
            id: operationId
          });
        } catch {
          // Dismiss the loading toast and show error
          toast.error("Failed to generate report data", {
            id: operationId
          });

          setExporting(prev => ({ ...prev, [id]: false }));
          return;
        }
      }

      // Now export the report
      const result = await exportReport(id, format);

      if (!result || !result.fileUrl) {
        throw new Error(`Failed to generate ${format.toUpperCase()} file`);
      }

      // Open the file in a new tab
      window.open(result.fileUrl, "_blank");

      // Dismiss the loading toast and show success
      toast.success(`Report exported as ${format.toUpperCase()}`, {
        id: operationId
      });

      // Refresh the reports list to show the updated data
      const updatedReports = await getReports();
      setReports(updatedReports);
    } catch {
      // Dismiss the loading toast and show error
      toast.error(`Failed to export report as ${format}`, {
        id: operationId
      });
    } finally {
      setExporting(prev => ({ ...prev, [id]: false }));
    }
  }, [reports]);

  const handleEditReport = useCallback((id: string) => {
    router.push(`/dashboard/reports/edit/${id}`);
  }, [router]);

  const handleViewReport = useCallback((id: string) => {
    router.push(`/dashboard/reports/view/${id}`);
  }, [router]);

  const resetFilters = useCallback(() => {
    setSearchQuery('');
    setTypeFilter('ALL');
    setStatusFilter('ALL');
    setScheduledFilter('ALL');
    setDateRange({ from: undefined, to: undefined });
    setSortField('createdAt');
    setSortDirection('desc');
  }, []);

  const handleDateRangeChange = useCallback((range: DateRange | undefined) => {
    if (!range) {
      setDateRange({ from: undefined, to: undefined });
    } else {
      setDateRange({ from: range.from, to: range.to });
    }
  }, []);

  const applyDatePreset = useCallback((preset: string) => {
    const today = new Date();

    switch (preset) {
      case 'last7days':
        setDateRange({
          from: addDays(today, -7),
          to: today
        });
        break;
      case 'last30days':
        setDateRange({
          from: addDays(today, -30),
          to: today
        });
        break;
      case 'thisMonth':
        setDateRange({
          from: startOfMonth(today),
          to: endOfMonth(today)
        });
        break;
      case 'thisYear':
        setDateRange({
          from: startOfYear(today),
          to: today
        });
        break;
      default:
        break;
    }
  }, []);

  // Memoized Row component
  const Row = React.memo(({ index, style }: ListChildComponentProps) => {
    const report = memoizedFilteredReports[index];
    return (
      <div style={style} key={report.id}>
        <TableRow>
          <TableCell className="font-medium">{report.title || report.name || "Untitled Report"}</TableCell>
          <TableCell>
            {(report.reportType || report.type || "Unknown").replace(/_/g, ' ')}</TableCell>
          <TableCell>
            <div className="flex flex-col">
              <span>{format(new Date(report.createdAt), "MMM d, yyyy")}</span>
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(report.createdAt), { addSuffix: true })}
              </span>
            </div>
          </TableCell>
          <TableCell>
            <Badge variant={(report.data?.length ?? 0) > 0 ? "default" : "outline"}>
              {(report.data?.length ?? 0) > 0 ? "Generated" : "Draft"}
            </Badge>
          </TableCell>
          <TableCell>
            <div className="flex items-center">
              <FileSpreadsheet className="mr-2 h-4 w-4 text-emerald-500" />
              <FileText className="mr-2 h-4 w-4 text-blue-500" />
              Excel/PDF
            </div>
          </TableCell>
          <TableCell>
            {(report.scheduledReports?.length ?? 0) > 0 ? (
              <Badge
                variant="secondary"
                className="flex items-center gap-1"
              >
                <Clock className="h-3 w-3" />
                Active
              </Badge>
            ) : (
              <span className="text-muted-foreground">—</span>
            )}
          </TableCell>
          <TableCell className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleViewReport(report.id)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Report
                </DropdownMenuItem>
                <DropdownMenuItem
                  disabled={exporting[report.id]}
                  onClick={() => handleExportReport(report.id, "pdf")}
                >
                  {exporting[report.id] ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <FileText className="mr-2 h-4 w-4" />
                  )}
                  Export PDF
                </DropdownMenuItem>
                <DropdownMenuItem
                  disabled={exporting[report.id]}
                  onClick={() => handleExportReport(report.id, "excel")}
                >
                  {exporting[report.id] ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <FileSpreadsheet className="mr-2 h-4 w-4" />
                  )}
                  Export Excel
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push(`/dashboard/reports/schedule/new?reportId=${report.id}`)}>
                  <Clock className="mr-2 h-4 w-4" />
                  Schedule Report
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEditReport(report.id)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => handleDeleteReport(report.id)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Report
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>
      </div>
    );
  });
  
  Row.displayName = 'ReportRow';

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Reports</CardTitle>
          <CardDescription>
            View and manage your recently created and generated reports
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (reports.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Reports</CardTitle>
          <CardDescription>
            View and manage your recently created and generated reports
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No reports yet</h3>
            <p className="text-muted-foreground mt-2 mb-4">
              You haven&apos;t created any reports yet. Create your first report to get started.
            </p>
            <Button onClick={() => router.push("/dashboard/reports/new")}>
              Create Report
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle>Recent Reports</CardTitle>
            <CardDescription>
              View and manage your recently created and generated reports
            </CardDescription>
          </div>
          <Button onClick={() => router.push("/dashboard/reports/new")}>
            Create Report
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div ref={tableContainerRef}>
        <div className="mb-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search reports..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-9 w-9"
                onClick={() => setSearchQuery('')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1 h-9">
                  <Filter className="h-4 w-4" />
                  Filters
                  {(typeFilter !== 'ALL' || statusFilter !== 'ALL' || scheduledFilter !== 'ALL' || (dateRange.from && dateRange.to)) && (
                    <Badge className="ml-1 h-5 w-5 p-0 flex items-center justify-center">
                      {[
                        typeFilter !== 'ALL' ? 1 : 0,
                        statusFilter !== 'ALL' ? 1 : 0,
                        scheduledFilter !== 'ALL' ? 1 : 0,
                        (dateRange.from && dateRange.to) ? 1 : 0
                      ].reduce((a, b) => a + b, 0)}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-72" align="end">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Report Type</h3>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="w-full justify-between">
                          {typeFilter === 'ALL' ? 'All Types' : typeFilter}
                          <ChevronDown className="h-4 w-4 opacity-50" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-full">
                        <DropdownMenuRadioGroup value={typeFilter} onValueChange={(value) => setTypeFilter(value as ReportType | 'ALL')}>
                          <DropdownMenuRadioItem value="ALL">All Types</DropdownMenuRadioItem>
                          <DropdownMenuRadioItem value="EXPENSES">Expenses</DropdownMenuRadioItem>
                          <DropdownMenuRadioItem value="VENDOR_ANALYSIS">Vendor Analysis</DropdownMenuRadioItem>
                          <DropdownMenuRadioItem value="CATEGORY_ANALYSIS">Category Analysis</DropdownMenuRadioItem>
                          <DropdownMenuRadioItem value="CASH_FLOW">Cash Flow</DropdownMenuRadioItem>
                          <DropdownMenuRadioItem value="CUSTOM">Custom</DropdownMenuRadioItem>
                        </DropdownMenuRadioGroup>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Status</h3>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="w-full justify-between">
                          {statusFilter === 'ALL' ? 'All Statuses' : statusFilter}
                          <ChevronDown className="h-4 w-4 opacity-50" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-full">
                        <DropdownMenuRadioGroup value={statusFilter} onValueChange={(value) => setStatusFilter(value as 'ALL' | 'GENERATED' | 'DRAFT')}>
                          <DropdownMenuRadioItem value="ALL">All Statuses</DropdownMenuRadioItem>
                          <DropdownMenuRadioItem value="GENERATED">Generated</DropdownMenuRadioItem>
                          <DropdownMenuRadioItem value="DRAFT">Draft</DropdownMenuRadioItem>
                        </DropdownMenuRadioGroup>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Scheduled</h3>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="w-full justify-between">
                          {scheduledFilter === 'ALL' ? 'All Reports' :
                           scheduledFilter === 'SCHEDULED' ? 'Scheduled Only' : 'Not Scheduled'}
                          <ChevronDown className="h-4 w-4 opacity-50" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-full">
                        <DropdownMenuRadioGroup value={scheduledFilter}
                            onValueChange={(value) => setScheduledFilter(value as 'ALL' | 'SCHEDULED' | 'NOT_SCHEDULED')}>
                          <DropdownMenuRadioItem value="ALL">All Reports</DropdownMenuRadioItem>
                          <DropdownMenuRadioItem value="SCHEDULED">Scheduled Only</DropdownMenuRadioItem>
                          <DropdownMenuRadioItem value="NOT_SCHEDULED">Not Scheduled</DropdownMenuRadioItem>
                        </DropdownMenuRadioGroup>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Date Range</h3>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyDatePreset('last7days')}
                      >
                        Last 7 days
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyDatePreset('last30days')}
                      >
                        Last 30 days
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyDatePreset('thisMonth')}
                      >
                        This month
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyDatePreset('thisYear')}
                      >
                        This year
                      </Button>
                    </div>
                    <DateRangePicker
                      dateRange={dateRange}
                      onDateRangeChange={handleDateRangeChange}
                    />
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={resetFilters}
                  >
                    Reset All Filters
                  </Button>
                </div>
              </PopoverContent>
            </Popover>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1 h-9">
                  {sortDirection === 'asc' ? (
                    <SortAsc className="h-4 w-4" />
                  ) : (
                    <SortDesc className="h-4 w-4" />
                  )}
                  Sort
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Sort By</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup
                  value={sortField}
                  onValueChange={(value) => setSortField(value as SortField)}
                >
                  <DropdownMenuRadioItem value="name">Name</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="type">Type</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="createdAt">Date Created</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
                <DropdownMenuSeparator />
                <DropdownMenuCheckboxItem
                  checked={sortDirection === 'asc'}
                  onCheckedChange={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                >
                  Ascending
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {filteredReports.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center border rounded-md">
            <Search className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No matching reports</h3>
            <p className="text-muted-foreground mt-2 mb-4">
              Try adjusting your search filters to find what you&apos;re looking for.
            </p>
            <Button variant="outline" onClick={resetFilters}>
              Reset Filters
            </Button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="cursor-pointer" onClick={() => {
                  setSortField('name');
                  setSortDirection(sortField === 'name' && sortDirection === 'asc' ? 'desc' : 'asc');
                }}>
                  Report Name
                  {sortField === 'name' && (
                    sortDirection === 'asc' ?
                    <SortAsc className="inline h-4 w-4 ml-1" /> :
                    <SortDesc className="inline h-4 w-4 ml-1" />
                  )}
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => {
                  setSortField('type');
                  setSortDirection(sortField === 'type' && sortDirection === 'asc' ? 'desc' : 'asc');
                }}>
                  Type
                  {sortField === 'type' && (
                    sortDirection === 'asc' ?
                    <SortAsc className="inline h-4 w-4 ml-1" /> :
                    <SortDesc className="inline h-4 w-4 ml-1" />
                  )}
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => {
                  setSortField('createdAt');
                  setSortDirection(sortField === 'createdAt' && sortDirection === 'asc' ? 'desc' : 'asc');
                }}>
                  Created
                  {sortField === 'createdAt' && (
                    sortDirection === 'asc' ?
                    <SortAsc className="inline h-4 w-4 ml-1" /> :
                    <SortDesc className="inline h-4 w-4 ml-1" />
                  )}
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Format</TableHead>
                <TableHead>Schedule</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {USE_VIRTUAL ? (
                <List
                  height={500}
                  itemCount={memoizedFilteredReports.length}
                  itemSize={60}
                  width={tableWidth}
                >
                  {Row}
                </List>
              ) : (
                memoizedFilteredReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell className="font-medium">{report.title || report.name || "Untitled Report"}</TableCell>
                    <TableCell>
                      {(report.reportType || report.type || "Unknown").replace(/_/g, ' ')}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{format(new Date(report.createdAt), "MMM d, yyyy")}</span>
                        <span className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(report.createdAt), { addSuffix: true })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={(report.data?.length ?? 0) > 0 ? "default" : "outline"}>
                        {(report.data?.length ?? 0) > 0 ? "Generated" : "Draft"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <FileSpreadsheet className="mr-2 h-4 w-4 text-emerald-500" />
                        <FileText className="mr-2 h-4 w-4 text-blue-500" />
                        Excel/PDF
                      </div>
                    </TableCell>
                    <TableCell>
                      {(report.scheduledReports?.length ?? 0) > 0 ? (
                        <Badge
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          <Clock className="h-3 w-3" />
                          Active
                        </Badge>
                      ) : (
                        <span className="text-muted-foreground">—</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleViewReport(report.id)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Report
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            disabled={exporting[report.id]}
                            onClick={() => handleExportReport(report.id, "pdf")}
                          >
                            {exporting[report.id] ? (
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                              <FileText className="mr-2 h-4 w-4" />
                            )}
                            Export PDF
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            disabled={exporting[report.id]}
                            onClick={() => handleExportReport(report.id, "excel")}
                          >
                            {exporting[report.id] ? (
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                              <FileSpreadsheet className="mr-2 h-4 w-4" />
                            )}
                            Export Excel
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => router.push(`/dashboard/reports/schedule/new?reportId=${report.id}`)}>
                            <Clock className="mr-2 h-4 w-4" />
                            Schedule Report
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditReport(report.id)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteReport(report.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Report
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        )}
      </div>
      </CardContent>
    </Card>
  );
}
