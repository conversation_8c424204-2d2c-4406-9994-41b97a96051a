'use client';

import {
} from '@/components/dashboard/dashboard-comp/chart';
import {
  Area,
  AreaChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from 'recharts';
import { useEffect, useState } from 'react';
import { useTheme } from 'next-themes';
import { useDateFilter } from '../providers/date-filter-provider';

export function Overview() {
  const [data, setData] = useState<{ name: string; income: number; expenses: number }[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { theme } = useTheme();
  const { getDateRangeForAPI } = useDateFilter();

  useEffect(() => {
    setLoading(true);

    // Build API URL with date range parameters
    let apiUrl = '/api/invoices/stats';
    const dateRange = getDateRangeForAPI();
    if (dateRange) {
      const params = new URLSearchParams({
        from: dateRange.from,
        to: dateRange.to,
      });
      apiUrl += `?${params.toString()}`;
    }

    fetch(apiUrl)
      .then(async (res) => {
        if (!res.ok) throw new Error('Failed to fetch chart data');
        const apiData = await res.json();
        // Map API monthly data to chart format
        const chartData = (apiData.monthly || []).map((m: { month: string; paid: number; pending: number; overdue: number }) => ({
          name: m.month.slice(0, 3),
          income: m.paid ?? 0,
          expenses: (m.pending ?? 0) + (m.overdue ?? 0),
        }));

        setData(chartData);
        setError(null);
      })
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, [getDateRangeForAPI]);

  if (loading)
    return (
      <div className="text-center py-8 text-muted-foreground">
        Loading chart...
      </div>
    );
  if (error)
    return (
      <div className="text-center text-destructive py-8">{error}</div>
    );

  // Theme-matching colors that complement the dashboard design
  const isDark = theme === 'dark';

  const incomeColors = {
    primary: isDark ? '#10b981' : '#059669', // Emerald theme colors
    gradient: isDark
      ? ['#10b981', '#059669', '#047857']
      : ['#059669', '#34d399', '#a7f3d0'],
    glow: isDark ? '#10b981' : '#059669'
  };

  const expenseColors = {
    primary: isDark ? '#f59e0b' : '#d97706', // Amber theme colors  
    gradient: isDark
      ? ['#f59e0b', '#d97706', '#b45309']
      : ['#d97706', '#fbbf24', '#fde68a'],
    glow: isDark ? '#f59e0b' : '#d97706'
  };

  const textColor = isDark ? '#f3f4f6' : '#374151';
  const gridColor = isDark ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)';

  return (
    <div className="h-full w-full flex flex-col items-center justify-center px-4 py-6 relative">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
        <div
          className="absolute top-1/3 left-1/3 w-20 h-20 rounded-full opacity-6 blur-2xl"
          style={{ backgroundColor: incomeColors.glow }}
        />
        <div
          className="absolute bottom-1/3 right-1/3 w-16 h-16 rounded-full opacity-6 blur-2xl"
          style={{ backgroundColor: expenseColors.glow, animationDelay: '2s' }}
        />
      </div>

      {/* Centered legend */}
      <div className="mb-6 flex justify-center gap-6 relative z-10">
        <div className="flex items-center gap-2.5 px-4 py-2.5 rounded-xl bg-emerald-50/80 dark:bg-emerald-950/30 border border-emerald-200/50 dark:border-emerald-700/30">
          <div
            className="h-3.5 w-3.5 rounded-full shadow-md"
            style={{
              backgroundColor: incomeColors.primary,
              boxShadow: `0 0 6px ${incomeColors.primary}50`
            }}
          />
          <span className="text-sm font-bold text-emerald-700 dark:text-emerald-300">
            Income
          </span>
        </div>
        <div className="flex items-center gap-2.5 px-4 py-2.5 rounded-xl bg-amber-50/80 dark:bg-amber-950/30 border border-amber-200/50 dark:border-amber-700/30">
          <div
            className="h-3.5 w-3.5 rounded-full shadow-md"
            style={{
              backgroundColor: expenseColors.primary,
              boxShadow: `0 0 6px ${expenseColors.primary}50`
            }}
          />
          <span className="text-sm font-bold text-amber-700 dark:text-amber-300">
            Expenses
          </span>
        </div>
      </div>

      {/* Centered chart container */}
      <div className="flex-1 w-full max-w-full relative z-10 flex items-center justify-center">
        <div className="w-full h-full max-h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={data}
              margin={{
                top: 10,
                right: 15,
                left: 40,
                bottom: 20,
              }}
            >
              <defs>
                {/* Enhanced multi-stop gradient for income */}
                <linearGradient
                  id="colorIncome"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop
                    offset="0%"
                    stopColor={incomeColors.gradient[0]}
                    stopOpacity={0.9}
                  />
                  <stop
                    offset="50%"
                    stopColor={incomeColors.gradient[1]}
                    stopOpacity={0.6}
                  />
                  <stop
                    offset="100%"
                    stopColor={incomeColors.gradient[2]}
                    stopOpacity={0.1}
                  />
                </linearGradient>

                {/* Enhanced multi-stop gradient for expenses */}
                <linearGradient
                  id="colorExpenses"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop
                    offset="0%"
                    stopColor={expenseColors.gradient[0]}
                    stopOpacity={0.9}
                  />
                  <stop
                    offset="50%"
                    stopColor={expenseColors.gradient[1]}
                    stopOpacity={0.6}
                  />
                  <stop
                    offset="100%"
                    stopColor={expenseColors.gradient[2]}
                    stopOpacity={0.1}
                  />
                </linearGradient>

                {/* Glow effects for lines */}
                <filter id="incomeGlow">
                  <feMorphology operator="dilate" radius="1" />
                  <feGaussianBlur stdDeviation="2" result="coloredBlur" />
                  <feMerge>
                    <feMergeNode in="coloredBlur" />
                    <feMergeNode in="SourceGraphic" />
                  </feMerge>
                </filter>

                <filter id="expenseGlow">
                  <feMorphology operator="dilate" radius="1" />
                  <feGaussianBlur stdDeviation="2" result="coloredBlur" />
                  <feMerge>
                    <feMergeNode in="coloredBlur" />
                    <feMergeNode in="SourceGraphic" />
                  </feMerge>
                </filter>
              </defs>
              <CartesianGrid
                strokeDasharray="1 3"
                stroke={gridColor}
                vertical={false}
                horizontal={true}
                className="opacity-40"
              />
              <XAxis
                dataKey="name"
                axisLine={false}
                tickLine={false}
                tick={{
                  fill: textColor,
                  fontSize: 11,
                  fontWeight: 500,
                }}
                className="text-xs"
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{
                  fill: textColor,
                  fontSize: 10,
                  fontWeight: 500,
                }}
                tickFormatter={(value) =>
                  `$${(value / 1000).toFixed(0)}k`
                }
                width={35}
                className="text-xs"
              />
              <Tooltip
                content={
                  <CustomTooltip active={false} payload={[]} label="" />
                }
              />
              <Area
                type="monotone"
                dataKey="income"
                stroke={incomeColors.primary}
                strokeWidth={2.5}
                fill="url(#colorIncome)"
                fillOpacity={0.7}
                className="drop-shadow-sm"
                dot={false}
                activeDot={{
                  r: 5,
                  fill: incomeColors.primary,
                  stroke: isDark ? '#ffffff' : '#ffffff',
                  strokeWidth: 2,
                  opacity: 1,
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                }}
              />
              <Area
                type="monotone"
                dataKey="expenses"
                stroke={expenseColors.primary}
                strokeWidth={2.5}
                fill="url(#colorExpenses)"
                fillOpacity={0.7}
                className="drop-shadow-sm"
                dot={false}
                activeDot={{
                  r: 5,
                  fill: expenseColors.primary,
                  stroke: isDark ? '#ffffff' : '#ffffff',
                  strokeWidth: 2,
                  opacity: 1,
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                }}
             />
           </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}

function CustomTooltip({
  active,
  payload,
  label,
}: {
  active: boolean;
  payload: Array<{ value: number; dataKey: string }>;
  label: string;
}) {
  if (active && payload && payload.length) {
    const isDark = document.documentElement.classList.contains('dark');

    const incomeColors = {
      primary: isDark ? '#10b981' : '#059669',
    };

    const expenseColors = {
      primary: isDark ? '#f59e0b' : '#d97706',
    };

    return (
      <div className="rounded-2xl border border-gray-200/50 dark:border-gray-700/50 bg-white/98 dark:bg-gray-800/98 backdrop-blur-sm p-5 shadow-xl shadow-black/5 dark:shadow-black/20">
        <div className="font-bold text-gray-900 dark:text-gray-100 mb-4 text-center text-base">
          {label}
        </div>
        <div className="space-y-3">
          {payload.map((entry, index) => {
            const isIncome = entry.dataKey === 'income';
            const color = isIncome ? incomeColors.primary : expenseColors.primary;
            const bgColor = isIncome ? 'bg-emerald-50 dark:bg-emerald-950/30' : 'bg-amber-50 dark:bg-amber-950/30';

            return (
              <div key={index} className={`flex items-center justify-between gap-4 min-w-[180px] p-3 rounded-xl ${bgColor}`}>
                <div className="flex items-center gap-3">
                  <div
                    className="h-4 w-4 rounded-full shadow-md"
                    style={{
                      backgroundColor: color,
                      boxShadow: `0 0 8px ${color}40`
                    }}
                  />
                  <span className="text-sm font-semibold capitalize" style={{ color }}>
                    {entry.dataKey}
                  </span>
                </div>
                <div className="font-bold text-gray-900 dark:text-gray-100 text-base">
                  ${entry.value.toLocaleString()}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  return null;
}
