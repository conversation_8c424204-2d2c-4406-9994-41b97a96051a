import db from '@/db/db';
import { CacheEngine } from './cache-engine';

interface PredictiveInsight {
  type: 'suggestion' | 'warning' | 'opportunity' | 'trend';
  title: string;
  description: string;
  confidence: number; // 0-1
  actionable: boolean;
  priority: 'low' | 'medium' | 'high';
  data?: any;
}

interface BusinessPattern {
  pattern: string;
  frequency: number;
  lastOccurrence: Date;
  predictedNext?: Date;
  confidence: number;
}

/**
 * Advanced Prediction Engine for proactive AI assistance
 * Analyzes user patterns and provides intelligent suggestions
 */
export class PredictionEngine {
  
  /**
   * Generate predictive insights for a user
   */
  static async generateInsights(userId: string): Promise<PredictiveInsight[]> {
    const cacheKey = CacheEngine.generateKey('predictive-insights', userId);
    
    return CacheEngine.getOrSet(
      cacheKey,
      'user-context', // Use user-context cache config (5 minutes)
      async () => {
        const insights: PredictiveInsight[] = [];
        
        // Analyze different aspects in parallel
        const [
          paymentPatterns,
          invoicePatterns,
          vendorPatterns,
          seasonalTrends,
          cashFlowPredictions
        ] = await Promise.all([
          this.analyzePaymentPatterns(userId),
          this.analyzeInvoicePatterns(userId),
          this.analyzeVendorPatterns(userId),
          this.analyzeSeasonalTrends(userId),
          this.predictCashFlow(userId)
        ]);

        insights.push(...paymentPatterns);
        insights.push(...invoicePatterns);
        insights.push(...vendorPatterns);
        insights.push(...seasonalTrends);
        insights.push(...cashFlowPredictions);

        // Sort by priority and confidence
        return insights.sort((a, b) => {
          const priorityWeight = { high: 3, medium: 2, low: 1 };
          const scoreA = priorityWeight[a.priority] * a.confidence;
          const scoreB = priorityWeight[b.priority] * b.confidence;
          return scoreB - scoreA;
        }).slice(0, 5); // Return top 5 insights
      }
    );
  }

  /**
   * Analyze payment patterns and predict delays
   */
  private static async analyzePaymentPatterns(userId: string): Promise<PredictiveInsight[]> {
    const insights: PredictiveInsight[] = [];
    
    try {
      // Get recent payment data
      const recentInvoices = await db.invoice.findMany({
        where: { 
          userId,
          createdAt: { gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // Last 90 days
        },
        orderBy: { createdAt: 'desc' }
      });

      // Analyze overdue patterns
      const overdueInvoices = recentInvoices.filter(inv => inv.status === 'OVERDUE');
      const overdueRate = overdueInvoices.length / recentInvoices.length;

      if (overdueRate > 0.2) { // More than 20% overdue
        insights.push({
          type: 'warning',
          title: 'High Overdue Rate Detected',
          description: `${Math.round(overdueRate * 100)}% of your recent invoices are overdue. Consider implementing automated payment reminders.`,
          confidence: 0.9,
          actionable: true,
          priority: 'high',
          data: { overdueRate, overdueCount: overdueInvoices.length }
        });
      }

      // Predict upcoming payment delays
      const pendingInvoices = recentInvoices.filter(inv => inv.status === 'PENDING');
      const riskInvoices = pendingInvoices.filter(inv => {
        if (!inv.dueDate) return false;
        const daysUntilDue = Math.ceil((inv.dueDate.getTime() - Date.now()) / (24 * 60 * 60 * 1000));
        return daysUntilDue <= 7 && daysUntilDue > 0;
      });

      if (riskInvoices.length > 0) {
        insights.push({
          type: 'suggestion',
          title: 'Payment Reminders Recommended',
          description: `${riskInvoices.length} invoices are due within 7 days. Send payment reminders now to avoid delays.`,
          confidence: 0.8,
          actionable: true,
          priority: 'medium',
          data: { riskInvoices: riskInvoices.length }
        });
      }

    } catch (error) {
      console.error('Payment pattern analysis error:', error);
    }

    return insights;
  }

  /**
   * Analyze invoice creation patterns
   */
  private static async analyzeInvoicePatterns(userId: string): Promise<PredictiveInsight[]> {
    const insights: PredictiveInsight[] = [];
    
    try {
      // Analyze monthly invoice creation patterns
      const monthlyData = await db.$queryRaw<Array<{month: string, count: number}>>`
        SELECT 
          DATE_FORMAT(createdAt, '%Y-%m') as month,
          COUNT(*) as count
        FROM Invoice 
        WHERE userId = ${userId}
          AND createdAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(createdAt, '%Y-%m')
        ORDER BY month DESC
        LIMIT 12
      `;

      if (monthlyData.length >= 3) {
        const recentMonths = monthlyData.slice(0, 3);
        const avgRecent = recentMonths.reduce((sum, m) => sum + m.count, 0) / recentMonths.length;
        const previousMonths = monthlyData.slice(3, 6);
        const avgPrevious = previousMonths.reduce((sum, m) => sum + m.count, 0) / previousMonths.length;

        const growthRate = (avgRecent - avgPrevious) / avgPrevious;

        if (growthRate > 0.2) { // 20% growth
          insights.push({
            type: 'opportunity',
            title: 'Business Growth Detected',
            description: `Your invoice volume has increased by ${Math.round(growthRate * 100)}% recently. Consider upgrading your plan for better features.`,
            confidence: 0.7,
            actionable: true,
            priority: 'medium',
            data: { growthRate, avgRecent, avgPrevious }
          });
        }
      }

      // Predict next invoice creation based on patterns
      const patterns = await this.detectBusinessPatterns(userId);
      const monthlyPattern = patterns.find(p => p.pattern === 'monthly_invoicing');
      
      if (monthlyPattern && monthlyPattern.confidence > 0.7) {
        const daysSinceLastInvoice = Math.ceil(
          (Date.now() - monthlyPattern.lastOccurrence.getTime()) / (24 * 60 * 60 * 1000)
        );
        
        if (daysSinceLastInvoice >= 25) { // Close to monthly cycle
          insights.push({
            type: 'suggestion',
            title: 'Monthly Invoice Cycle Due',
            description: 'Based on your pattern, you typically create invoices around this time of month. Ready to create this month\'s invoices?',
            confidence: monthlyPattern.confidence,
            actionable: true,
            priority: 'medium',
            data: { daysSinceLastInvoice, pattern: monthlyPattern }
          });
        }
      }

    } catch (error) {
      console.error('Invoice pattern analysis error:', error);
    }

    return insights;
  }

  /**
   * Analyze vendor relationships and spending patterns
   */
  private static async analyzeVendorPatterns(userId: string): Promise<PredictiveInsight[]> {
    const insights: PredictiveInsight[] = [];
    
    try {
      // Analyze vendor concentration risk
      const vendorSpending = await db.invoice.groupBy({
        by: ['vendorName'],
        where: {
          userId,
          createdAt: { gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) },
          vendorName: { not: null }
        },
        _sum: { amount: true },
        _count: { id: true },
        orderBy: { _sum: { amount: 'desc' } }
      });

      if (vendorSpending.length > 0) {
        const totalSpending = vendorSpending.reduce((sum, v) => sum + (v._sum.amount || 0), 0);
        const topVendorSpending = vendorSpending[0]._sum.amount || 0;
        const concentration = topVendorSpending / totalSpending;

        if (concentration > 0.5) { // More than 50% with one vendor
          insights.push({
            type: 'warning',
            title: 'High Vendor Concentration Risk',
            description: `${Math.round(concentration * 100)}% of your spending is with ${vendorSpending[0].vendorName}. Consider diversifying suppliers.`,
            confidence: 0.8,
            actionable: true,
            priority: 'medium',
            data: { concentration, topVendor: vendorSpending[0].vendorName }
          });
        }

        // Identify cost optimization opportunities
        const expensiveVendors = vendorSpending.filter(v => 
          (v._sum.amount || 0) > totalSpending * 0.1 && v._count.id >= 3
        );

        if (expensiveVendors.length > 1) {
          insights.push({
            type: 'opportunity',
            title: 'Cost Optimization Opportunity',
            description: `You have ${expensiveVendors.length} major vendors. Analyzing their pricing could reveal savings opportunities.`,
            confidence: 0.6,
            actionable: true,
            priority: 'low',
            data: { expensiveVendors: expensiveVendors.length }
          });
        }
      }

    } catch (error) {
      console.error('Vendor pattern analysis error:', error);
    }

    return insights;
  }

  /**
   * Analyze seasonal trends
   */
  private static async analyzeSeasonalTrends(userId: string): Promise<PredictiveInsight[]> {
    const insights: PredictiveInsight[] = [];
    
    try {
      // Analyze quarterly patterns
      const quarterlyData = await db.$queryRaw<Array<{quarter: string, total: number}>>`
        SELECT 
          CONCAT(YEAR(createdAt), '-Q', QUARTER(createdAt)) as quarter,
          SUM(amount) as total
        FROM Invoice 
        WHERE userId = ${userId}
          AND createdAt >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
        GROUP BY YEAR(createdAt), QUARTER(createdAt)
        ORDER BY quarter DESC
        LIMIT 8
      `;

      if (quarterlyData.length >= 4) {
        const currentQuarter = quarterlyData[0];
        const lastYearSameQuarter = quarterlyData.find(q => 
          q.quarter.endsWith(currentQuarter.quarter.slice(-2))
        );

        if (lastYearSameQuarter) {
          const yearOverYearGrowth = (currentQuarter.total - lastYearSameQuarter.total) / lastYearSameQuarter.total;
          
          if (Math.abs(yearOverYearGrowth) > 0.15) { // Significant change
            insights.push({
              type: yearOverYearGrowth > 0 ? 'opportunity' : 'warning',
              title: `${yearOverYearGrowth > 0 ? 'Strong' : 'Declining'} Seasonal Performance`,
              description: `Your revenue is ${yearOverYearGrowth > 0 ? 'up' : 'down'} ${Math.round(Math.abs(yearOverYearGrowth) * 100)}% compared to the same quarter last year.`,
              confidence: 0.7,
              actionable: true,
              priority: Math.abs(yearOverYearGrowth) > 0.3 ? 'high' : 'medium',
              data: { yearOverYearGrowth, currentQuarter: currentQuarter.total, lastYear: lastYearSameQuarter.total }
            });
          }
        }
      }

    } catch (error) {
      console.error('Seasonal trend analysis error:', error);
    }

    return insights;
  }

  /**
   * Predict cash flow trends
   */
  private static async predictCashFlow(userId: string): Promise<PredictiveInsight[]> {
    const insights: PredictiveInsight[] = [];
    
    try {
      // Get pending invoices and their due dates
      const pendingInvoices = await db.invoice.findMany({
        where: { userId, status: 'PENDING' },
        select: { amount: true, dueDate: true }
      });

      const totalPending = pendingInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);
      
      // Calculate expected cash flow for next 30 days
      const next30Days = pendingInvoices.filter(inv => {
        if (!inv.dueDate) return false;
        const daysUntilDue = Math.ceil((inv.dueDate.getTime() - Date.now()) / (24 * 60 * 60 * 1000));
        return daysUntilDue <= 30 && daysUntilDue >= 0;
      });

      const expectedCashFlow = next30Days.reduce((sum, inv) => sum + (inv.amount || 0), 0);

      if (expectedCashFlow > totalPending * 0.7) { // More than 70% due soon
        insights.push({
          type: 'opportunity',
          title: 'Strong Cash Flow Expected',
          description: `$${expectedCashFlow.toLocaleString()} in payments expected within 30 days. Great cash flow position!`,
          confidence: 0.8,
          actionable: false,
          priority: 'low',
          data: { expectedCashFlow, totalPending }
        });
      } else if (expectedCashFlow < totalPending * 0.3) { // Less than 30% due soon
        insights.push({
          type: 'warning',
          title: 'Cash Flow Concern',
          description: `Only $${expectedCashFlow.toLocaleString()} expected within 30 days. Consider following up on overdue invoices.`,
          confidence: 0.7,
          actionable: true,
          priority: 'medium',
          data: { expectedCashFlow, totalPending }
        });
      }

    } catch (error) {
      console.error('Cash flow prediction error:', error);
    }

    return insights;
  }

  /**
   * Detect recurring business patterns
   */
  private static async detectBusinessPatterns(userId: string): Promise<BusinessPattern[]> {
    const patterns: BusinessPattern[] = [];
    
    try {
      // Detect monthly invoicing pattern
      const monthlyInvoices = await db.$queryRaw<Array<{month: string, count: number, lastDate: Date}>>`
        SELECT 
          DATE_FORMAT(createdAt, '%Y-%m') as month,
          COUNT(*) as count,
          MAX(createdAt) as lastDate
        FROM Invoice 
        WHERE userId = ${userId}
          AND createdAt >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(createdAt, '%Y-%m')
        HAVING count >= 3
        ORDER BY month DESC
      `;

      if (monthlyInvoices.length >= 3) {
        const avgMonthlyInvoices = monthlyInvoices.reduce((sum, m) => sum + m.count, 0) / monthlyInvoices.length;
        const consistency = 1 - (Math.max(...monthlyInvoices.map(m => m.count)) - Math.min(...monthlyInvoices.map(m => m.count))) / avgMonthlyInvoices;
        
        if (consistency > 0.7) {
          patterns.push({
            pattern: 'monthly_invoicing',
            frequency: avgMonthlyInvoices,
            lastOccurrence: monthlyInvoices[0].lastDate,
            confidence: consistency
          });
        }
      }

    } catch (error) {
      console.error('Pattern detection error:', error);
    }

    return patterns;
  }
}
