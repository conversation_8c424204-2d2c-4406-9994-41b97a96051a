import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

const isPublicRoute = createRouteMatcher([
  '/',               
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/onboarding(.*)',
  '/accept-invite(.*)',
  '/api/webhook(.*)',
  '/api/email(.*)',  // Allow email API routes to be public
  '/api/test-document', // Allow test document API for testing
  '/waitlist',        // Allow waitlist page
  '/pricing(.*)',     // Allow pricing page access
  '/checkout(.*)',    // Allow checkout page access
  '/success(.*)',     // Allow success page access
  '/confirmation(.*)', // Allow confirmation page access
])

export default clerkMiddleware(async (auth, req) => {
  // Enable authentication for non-public routes
  if (!isPublicRoute(req)) {
    await auth.protect();
  }
  
  return NextResponse.next();
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}
