import db from '@/db/db';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import { CacheEngine, CacheWarmer } from './cache-engine';

interface UserContext {
  user: {
    name: string;
    email: string;
    role: string;
    preferences: any;
    expertise: 'beginner' | 'intermediate' | 'expert';
  };
  business: {
    totalInvoices: number;
    totalRevenue: number;
    pendingAmount: number;
    overdueCount: number;
    recentInvoices: any[];
    topVendors: any[];
    monthlyTrends: any[];
  };
  system: {
    currentPage: string;
    availableActions: string[];
    pageContext: Record<string, any>;
    userPermissions: string[];
    suggestedActions: any[];
    workflowStatus: any[];
  };
  memory: {
    recentTopics: string[];
    userPatterns: any[];
    learnings: any[];
    preferences: Record<string, any>;
  };
  quickActions: string[];
  recentActivity: any[];
}

/**
 * Smart Context Engine - Replaces tools with intelligent context injection
 * This provides the AI with all necessary context in a single, optimized payload
 */
export class ContextEngine {

  static async getUserContext(userId: string, currentPage?: string): Promise<UserContext> {
    const cacheKey = CacheEngine.generateKey('user-context', userId, currentPage || 'default');

    return CacheEngine.getOrSet(
      cacheKey,
      'user-context',
      () => this.buildUserContext(userId, currentPage)
    );
  }

  private static async buildUserContext(userId: string, currentPage?: string): Promise<UserContext> {
    try {
      // Parallel data fetching for speed
      const [user, invoiceStats, recentInvoices, topVendors, systemState, userMemory] = await Promise.all([
        db.user.findUnique({
          where: { id: userId },
          select: {
            firstName: true,
            lastName: true,
            email: true,
            role: true,
            aiSettings: true,
          }
        }),

        // Get invoice statistics
        db.invoice.aggregate({
          where: { userId },
          _count: { id: true },
          _sum: { amount: true },
        }),

        // Get recent invoices
        db.invoice.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          take: 5,
          select: {
            id: true,
            invoiceNumber: true,
            vendorName: true,
            amount: true,
            status: true,
            createdAt: true,
          }
        }),

        // Get top vendors
        db.invoice.groupBy({
          by: ['vendorName'],
          where: { userId, vendorName: { not: null } },
          _sum: { amount: true },
          _count: { id: true },
          orderBy: { _sum: { amount: 'desc' } },
          take: 3,
        }),

        // Get system state and available actions
        this.getSystemContext(userId, currentPage),

        // Get user memory and patterns
        this.getUserMemoryContext(userId)
      ]);

      // Get overdue count
      const overdueCount = await db.invoice.count({
        where: { userId, status: 'OVERDUE' }
      });

      // Calculate pending amount
      const pendingAmount = await db.invoice.aggregate({
        where: { userId, status: 'PENDING' },
        _sum: { amount: true }
      });

      // Determine user expertise level
      const expertise = this.determineExpertiseLevel(invoiceStats._count.id, user?.aiSettings);

      return {
        user: {
          name: `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'User',
          email: user?.email || '',
          role: user?.role || 'USER',
          preferences: user?.aiSettings || {},
          expertise,
        },
        business: {
          totalInvoices: invoiceStats._count.id || 0,
          totalRevenue: invoiceStats._sum.amount || 0,
          pendingAmount: pendingAmount._sum.amount || 0,
          overdueCount,
          recentInvoices: recentInvoices.map(inv => ({
            number: inv.invoiceNumber,
            vendor: inv.vendorName,
            amount: inv.amount,
            status: inv.status,
            date: inv.createdAt.toISOString().split('T')[0]
          })),
          topVendors: topVendors.map(v => ({
            name: v.vendorName,
            total: v._sum.amount,
            count: v._count.id
          })),
          monthlyTrends: [], // Will be populated with trend data
        },
        system: systemState,
        memory: userMemory,
        quickActions: [
          'Create new invoice',
          'View pending invoices',
          'Generate monthly report',
          'Check cash flow',
          'Analyze spending'
        ],
        recentActivity: [] // Recent user actions
      };
    } catch (error) {
      console.error('Context Engine Error:', error);
      return this.getEmptyContext();
    }
  }

  private static async getSystemContext(userId: string, currentPage?: string): Promise<UserContext['system']> {
    try {
      const { SystemController } = await import('./control/system-controller');
      const systemState = await SystemController.getSystemState(userId, currentPage);
      const availableActions = SystemController.getAvailableActions(
        currentPage || '/dashboard',
        systemState.userPermissions
      );
      const suggestedActions = await SystemController.suggestNextActions(
        userId,
        currentPage || '/dashboard'
      );

      return {
        currentPage: currentPage || '/dashboard',
        availableActions: availableActions.map(action => action.name),
        pageContext: systemState.pageState,
        userPermissions: systemState.userPermissions,
        suggestedActions: suggestedActions.slice(0, 3),
        workflowStatus: systemState.activeWorkflows
      };
    } catch (error) {
      console.error('System context error:', error);
      return {
        currentPage: currentPage || '/dashboard',
        availableActions: [],
        pageContext: {},
        userPermissions: [],
        suggestedActions: [],
        workflowStatus: []
      };
    }
  }

  private static async getUserMemoryContext(userId: string): Promise<UserContext['memory']> {
    try {
      const { LongTermMemory } = await import('./memory/long-term-memory');
      const userHistory = await LongTermMemory.getUserHistory(userId);

      return {
        recentTopics: userHistory.pageUsage.map(p => p.page).slice(0, 5),
        userPatterns: userHistory.businessPatterns,
        learnings: userHistory.workflowPatterns,
        preferences: userHistory.preferences
      };
    } catch (error) {
      console.error('Memory context error:', error);
      return {
        recentTopics: [],
        userPatterns: [],
        learnings: [],
        preferences: {}
      };
    }
  }

  private static determineExpertiseLevel(
    totalInvoices: number,
    aiSettings: any
  ): 'beginner' | 'intermediate' | 'expert' {
    if (totalInvoices > 100) return 'expert';
    if (totalInvoices > 20) return 'intermediate';
    return 'beginner';
  }

  private static getEmptyContext(): UserContext {
    return {
      user: {
        name: 'User',
        email: '',
        role: 'USER',
        preferences: {},
        expertise: 'beginner'
      },
      business: {
        totalInvoices: 0,
        totalRevenue: 0,
        pendingAmount: 0,
        overdueCount: 0,
        recentInvoices: [],
        topVendors: [],
        monthlyTrends: []
      },
      system: {
        currentPage: '/dashboard',
        availableActions: [],
        pageContext: {},
        userPermissions: [],
        suggestedActions: [],
        workflowStatus: []
      },
      memory: {
        recentTopics: [],
        userPatterns: [],
        learnings: [],
        preferences: {}
      },
      quickActions: [],
      recentActivity: []
    };
  }

  static async clearCache(userId?: string) {
    if (userId) {
      const cacheKey = CacheEngine.generateKey('user-context', userId);
      await CacheEngine.delete(cacheKey);
    } else {
      await CacheEngine.clear('user-context');
    }
  }

  /**
   * Initialize cache warming for frequently accessed users
   */
  static initializeCacheWarming() {
    // Warm cache for active users every 5 minutes
    CacheWarmer.addWarmupTask(
      'active-users-context',
      'user-context',
      async () => {
        // Get most active users from last 24 hours
        const activeUsers = await db.message.groupBy({
          by: ['chatId'],
          where: {
            createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
          },
          _count: { id: true },
          orderBy: { _count: { id: 'desc' } },
          take: 10
        });

        // Warm cache for these users
        const userIds = await db.chat.findMany({
          where: { id: { in: activeUsers.map(u => u.chatId) } },
          select: { userId: true }
        });

        for (const { userId } of userIds) {
          await this.getUserContext(userId);
        }

        return { warmedUsers: userIds.length };
      },
      5 // Every 5 minutes
    );

    CacheWarmer.startWarming();
  }
}

/**
 * Generate optimized system prompt with enhanced user context
 */
export function generateContextualPrompt(context: UserContext): string {
  const expertiseLevel = context.user.expertise;
  const currentPage = context.system.currentPage;
  const availableActions = context.system.availableActions.slice(0, 3).join(', ');

  return `You are Billix AI, ${context.user.name}'s intelligent financial assistant with full system control.

CURRENT CONTEXT:
• Page: ${currentPage} | User Level: ${expertiseLevel}
• Business: ${context.business.totalInvoices} invoices, $${context.business.totalRevenue.toLocaleString()} revenue
• Pending: $${context.business.pendingAmount.toLocaleString()} (${context.business.overdueCount} overdue)
• Recent: ${context.business.recentInvoices.map(i => `${i.vendor}: $${i.amount}`).join(', ')}

SYSTEM CAPABILITIES:
• Page Control: Navigate, execute actions, manage UI
• Available Actions: ${availableActions}
• Document Generation: Create PDFs, Excel files, invoices
• Financial Intelligence: Analytics, forecasting, insights
• Memory: Remember patterns, preferences, conversations

MEMORY CONTEXT:
• Recent Topics: ${context.memory.recentTopics.join(', ')}
• User Patterns: ${context.memory.userPatterns.length} business patterns learned
• Expertise: Adapt responses to ${expertiseLevel} level

You can control the application, navigate pages, execute actions, and generate documents. Be proactive and suggest relevant actions based on context. Reference specific data and remember user preferences.`;
}
