import { CacheEngine } from './cache-engine';
import * as ExcelJS from 'exceljs';
import jsPDF from 'jspdf';
import db from '@/db/db';

// Advanced Document Generation Interfaces
interface DocumentRequest {
  type: 'invoice' | 'report' | 'contract' | 'proposal' | 'analysis' | 'dashboard';
  format: 'pdf' | 'excel' | 'image' | 'all';
  data: any;
  template?: DocumentTemplate;
  branding?: BrandingOptions;
  options?: DocumentOptions;
}

interface DocumentTemplate {
  id: string;
  name: string;
  type: string;
  layout: 'modern' | 'classic' | 'minimal' | 'professional' | 'creative';
  colorScheme: ColorScheme;
  typography: TypographySettings;
  components: TemplateComponent[];
}

interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  border: string;
}

interface TypographySettings {
  fontFamily: string;
  headingFont?: string;
  fontSize: {
    heading1: number;
    heading2: number;
    heading3: number;
    body: number;
    caption: number;
  };
  lineHeight: number;
}

interface TemplateComponent {
  type: 'header' | 'body' | 'footer' | 'table' | 'chart' | 'signature' | 'logo';
  position: { x: number; y: number; width: number; height: number };
  styling: Record<string, any>;
  content?: any;
}

interface BrandingOptions {
  logo?: string;
  companyName: string;
  companyAddress: string;
  companyEmail: string;
  companyPhone?: string;
  website?: string;
  colors: ColorScheme;
  fonts: TypographySettings;
}

interface DocumentOptions {
  includeCharts: boolean;
  includeImages: boolean;
  includeBranding: boolean;
  watermark?: string;
  password?: string;
  quality: 'draft' | 'standard' | 'high' | 'print';
  language: string;
  rtl: boolean;
}

interface GeneratedDocument {
  id: string;
  type: string;
  format: string;
  buffer: Buffer;
  metadata: DocumentMetadata;
  downloadUrl?: string;
}

interface DocumentMetadata {
  title: string;
  author: string;
  subject: string;
  creator: string;
  createdAt: Date;
  size: number;
  pages?: number;
  sheets?: number;
  dimensions?: { width: number; height: number };
}

/**
 * Advanced Document Generation Engine
 * Creates professional documents with smart templates and AI optimization
 */
export class DocumentGenerationEngine {
  private static templates = new Map<string, DocumentTemplate>();
  private static brandingCache = new Map<string, BrandingOptions>();

  /**
   * Generate document based on user request
   */
  static async generateDocument(
    userId: string,
    request: DocumentRequest
  ): Promise<GeneratedDocument[]> {
    const cacheKey = CacheEngine.generateKey('document', userId, request.type, request.format);
    
    return CacheEngine.getOrSet(
      cacheKey,
      'action-results',
      async () => {
        const documents: GeneratedDocument[] = [];
        
        // Get user branding
        const branding = await this.getUserBranding(userId);
        const template = await this.getOptimalTemplate(request.type, userId);
        
        // Generate based on format
        if (request.format === 'pdf' || request.format === 'all') {
          const pdf = await this.generatePDF(request, template, branding);
          documents.push(pdf);
        }
        
        if (request.format === 'excel' || request.format === 'all') {
          const excel = await this.generateExcel(request, template, branding);
          documents.push(excel);
        }
        
        if (request.format === 'image' || request.format === 'all') {
          const image = await this.generateImage(request, template, branding);
          documents.push(image);
        }
        
        // Store generation history
        await this.recordGeneration(userId, request, documents);
        
        return documents;
      }
    );
  }

  /**
   * Generate smart invoice PDF with dynamic templates
   */
  static async generateInvoicePDF(invoiceData: any, userId: string): Promise<GeneratedDocument> {
    const request: DocumentRequest = {
      type: 'invoice',
      format: 'pdf',
      data: invoiceData,
      options: {
        includeCharts: false,
        includeImages: true,
        includeBranding: true,
        quality: 'high',
        language: 'en',
        rtl: false
      }
    };

    const documents = await this.generateDocument(userId, request);
    return documents[0];
  }

  /**
   * Generate comprehensive financial report
   */
  static async generateFinancialReport(
    reportData: any,
    userId: string,
    format: 'pdf' | 'excel' | 'all' = 'pdf'
  ): Promise<GeneratedDocument[]> {
    const request: DocumentRequest = {
      type: 'report',
      format,
      data: reportData,
      options: {
        includeCharts: true,
        includeImages: true,
        includeBranding: true,
        quality: 'high',
        language: 'en',
        rtl: false
      }
    };

    return this.generateDocument(userId, request);
  }

  /**
   * Generate Excel dashboard with charts and pivot tables
   */
  static async generateExcelDashboard(data: any, userId: string): Promise<GeneratedDocument> {
    const workbook = new ExcelJS.Workbook();
    
    // Metadata
    workbook.creator = 'Billix AI';
    workbook.created = new Date();
    workbook.modified = new Date();

    // Dashboard sheet
    const dashboard = workbook.addWorksheet('Dashboard', {
      pageSetup: { paperSize: 9, orientation: 'landscape' }
    });

    // Summary sheet
    const summary = workbook.addWorksheet('Summary');

    // Data sheet
    const dataSheet = workbook.addWorksheet('Data');

    // Add charts and formatting
    await this.addDashboardCharts(dashboard, data);
    await this.addSummaryTables(summary, data);
    await this.addRawData(dataSheet, data);

    const buffer = await workbook.xlsx.writeBuffer() as unknown as Buffer;

    return {
      id: `excel-${Date.now()}`,
      type: 'dashboard',
      format: 'excel',
      buffer,
      metadata: {
        title: `Financial Dashboard - ${new Date().toISOString().split('T')[0]}`,
        author: 'Billix AI',
        subject: 'Financial Dashboard',
        creator: 'Billix AI',
        createdAt: new Date(),
        size: buffer.length,
        sheets: 3
      }
    };
  }

  /**
   * Generate image visualization (charts, infographics)
   */
  static async generateImageVisualization(
    data: any,
    type: 'chart' | 'infographic' | 'summary',
    userId: string
  ): Promise<GeneratedDocument> {
    // For now, return a placeholder - would implement with canvas or chart library
    const buffer = Buffer.from('PNG placeholder', 'utf8');

    return {
      id: `image-${Date.now()}`,
      type,
      format: 'image',
      buffer,
      metadata: {
        title: `${type.charAt(0).toUpperCase() + type.slice(1)} - ${new Date().toISOString().split('T')[0]}`,
        author: 'Billix AI',
        subject: `Financial ${type}`,
        creator: 'Billix AI',
        createdAt: new Date(),
        size: buffer.length,
        dimensions: { width: 1200, height: 800 }
      }
    };
  }

  /**
   * Smart template selection based on content and user preferences
   */
  private static async getOptimalTemplate(
    documentType: string,
    userId: string
  ): Promise<DocumentTemplate> {
    // Get user preferences from memory
    const { MemoryEngine } = await import('./memory-engine');
    const userProfile = await MemoryEngine.getUserProfile(userId);

    // Select template based on business type and preferences
    const templateId = this.selectTemplateByContext(documentType, userProfile);
    
    return this.getTemplate(templateId);
  }

  /**
   * Get user branding settings
   */
  private static async getUserBranding(userId: string): Promise<BrandingOptions> {
    if (this.brandingCache.has(userId)) {
      return this.brandingCache.get(userId)!;
    }

    // Get from database or use defaults
    const user = await db.user.findUnique({
      where: { id: userId }
    });

    const branding: BrandingOptions = {
      companyName: user?.firstName && user?.lastName 
        ? `${user.firstName} ${user.lastName}` 
        : 'Your Business',
      companyAddress: '',
      companyEmail: user?.email || '',
      colors: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#3b82f6',
        background: '#ffffff',
        text: '#1e293b',
        border: '#e2e8f0'
      },
      fonts: {
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        fontSize: {
          heading1: 24,
          heading2: 20,
          heading3: 16,
          body: 14,
          caption: 12
        },
        lineHeight: 1.5
      }
    };

    this.brandingCache.set(userId, branding);
    return branding;
  }

  /**
   * Advanced PDF generation with smart layouts
   */
  private static async generatePDF(
    request: DocumentRequest,
    template: DocumentTemplate,
    branding: BrandingOptions
  ): Promise<GeneratedDocument> {
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Apply template styling
    this.applyTemplateStyles(doc, template);

    // Add header with branding
    await this.addPDFHeader(doc, branding, template);

    // Add content based on document type with enhanced formatting
    let yPosition = 50;
    switch (request.type) {
      case 'invoice':
        yPosition = await this.addAdvancedInvoiceContent(doc, request.data, template, branding, yPosition);
        break;
      case 'report':
        yPosition = await this.addAdvancedReportContent(doc, request.data, template, branding, yPosition);
        break;
      case 'analysis':
        yPosition = await this.addAnalysisContent(doc, request.data, template, branding, yPosition);
        break;
      case 'contract':
        yPosition = await this.addContractContent(doc, request.data, template, branding, yPosition);
        break;
      default:
        yPosition = await this.addGenericContent(doc, request.data, template, branding, yPosition);
    }

    // Add footer
    await this.addPDFFooter(doc, branding, template);

    const buffer = Buffer.from(doc.output('arraybuffer'));

    // Upload to blob storage for permanent access
    const filename = `${request.type}_${Date.now()}.pdf`;
    const blob = new Blob([buffer], { type: 'application/pdf' });
    const uploadResult = await uploadToVercelBlob(blob, filename);

    return {
      id: `pdf-${Date.now()}`,
      type: request.type,
      format: 'pdf',
      buffer,
      downloadUrl: uploadResult.url,
      metadata: {
        title: this.generateTitle(request.type, request.data),
        author: branding.companyName,
        subject: request.type,
        creator: 'Billix AI',
        createdAt: new Date(),
        size: buffer.length,
        pages: doc.getNumberOfPages(),
        template: template.id,
        url: uploadResult.url
      }
    };
  }

  /**
   * Apply template styles to PDF
   */
  private static applyTemplateStyles(doc: jsPDF, template: DocumentTemplate): void {
    // Set default font based on template
    const fontFamily = template.typography.fontFamily || 'helvetica';
    doc.setFont(fontFamily);

    // Set default colors
    doc.setTextColor(template.colorScheme.text || '#000000');
  }

  /**
   * Add enhanced PDF header with branding and template styling
   */
  private static async addPDFHeader(
    doc: jsPDF,
    branding: BrandingOptions,
    template: DocumentTemplate
  ): Promise<void> {
    const pageWidth = doc.internal.pageSize.getWidth();

    // Add background color if specified in template
    if (template.colorScheme.background && template.colorScheme.background !== '#ffffff') {
      doc.setFillColor(template.colorScheme.background);
      doc.rect(0, 0, pageWidth, 40, 'F');
    }

    // Add company logo placeholder (in real implementation, load actual image)
    if (branding.logo?.url) {
      doc.setFontSize(10);
      doc.setTextColor('#666666');
      doc.text('[LOGO]', pageWidth - 30, 15);
    }

    // Add company name with template styling
    if (branding.companyName) {
      doc.setFontSize(template.typography.headingSize || 16);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(template.colorScheme.primary || '#000000');
      doc.text(branding.companyName, 20, 20);
    }

    // Add decorative line
    doc.setLineWidth(1);
    doc.setDrawColor(template.colorScheme.accent || template.colorScheme.primary || '#000000');
    doc.line(20, 35, pageWidth - 20, 35);
  }

  /**
   * Add enhanced PDF footer
   */
  private static async addPDFFooter(
    doc: jsPDF,
    branding: BrandingOptions,
    template: DocumentTemplate
  ): Promise<void> {
    const pageHeight = doc.internal.pageSize.getHeight();
    const pageWidth = doc.internal.pageSize.getWidth();

    // Add footer background if template specifies
    if (template.colorScheme.background && template.colorScheme.background !== '#ffffff') {
      doc.setFillColor(template.colorScheme.background);
      doc.rect(0, pageHeight - 25, pageWidth, 25, 'F');
    }

    // Add footer line
    doc.setLineWidth(0.5);
    doc.setDrawColor(template.colorScheme.accent || '#cccccc');
    doc.line(20, pageHeight - 20, pageWidth - 20, pageHeight - 20);

    // Add footer text
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor('#666666');

    const footerText = `Generated by Billix AI • ${new Date().toLocaleDateString()}`;
    doc.text(footerText, 20, pageHeight - 10);

    // Add page number
    const pageNumber = `Page ${doc.getCurrentPageInfo().pageNumber}`;
    const pageNumberWidth = doc.getTextWidth(pageNumber);
    doc.text(pageNumber, pageWidth - 20 - pageNumberWidth, pageHeight - 10);

    // Add website/contact info if available
    if (branding.website) {
      const websiteWidth = doc.getTextWidth(branding.website);
      doc.text(branding.website, (pageWidth - websiteWidth) / 2, pageHeight - 10);
    }
  }

  /**
   * Generate Excel with advanced features
   */
  private static async generateExcel(
    request: DocumentRequest,
    template: DocumentTemplate,
    branding: BrandingOptions
  ): Promise<GeneratedDocument> {
    const workbook = new ExcelJS.Workbook();
    
    // Set metadata
    workbook.creator = branding.companyName;
    workbook.created = new Date();

    // Add sheets based on document type
    switch (request.type) {
      case 'report':
        await this.addReportSheets(workbook, request.data, template);
        break;
      case 'invoice':
        await this.addInvoiceSheet(workbook, request.data, template);
        break;
      case 'analysis':
        await this.addAnalysisSheets(workbook, request.data, template);
        break;
    }

    const buffer = await workbook.xlsx.writeBuffer() as unknown as Buffer;

    return {
      id: `excel-${Date.now()}`,
      type: request.type,
      format: 'excel',
      buffer,
      metadata: {
        title: this.generateTitle(request.type, request.data),
        author: branding.companyName,
        subject: request.type,
        creator: 'Billix AI',
        createdAt: new Date(),
        size: buffer.length,
        sheets: workbook.worksheets.length
      }
    };
  }

  /**
   * Generate image with smart layouts
   */
  private static async generateImage(
    request: DocumentRequest,
    template: DocumentTemplate,
    branding: BrandingOptions
  ): Promise<GeneratedDocument> {
    // For invoice images, create a PNG representation
    if (request.type === 'invoice') {
      const buffer = await this.generateInvoiceImage(request.data, template, branding);
      
      return {
        id: `image-${Date.now()}`,
        type: request.type,
        format: 'image',
        buffer,
        metadata: {
          title: this.generateTitle(request.type, request.data),
          author: branding.companyName,
          subject: request.type,
          creator: 'Billix AI',
          createdAt: new Date(),
          size: buffer.length,
          dimensions: { width: 800, height: 1100 }
        }
      };
    }

    // For other types, return a simple placeholder
    const buffer = Buffer.from('PNG placeholder', 'utf8');

    return {
      id: `image-${Date.now()}`,
      type: request.type,
      format: 'image',
      buffer,
      metadata: {
        title: this.generateTitle(request.type, request.data),
        author: branding.companyName,
        subject: request.type,
        creator: 'Billix AI',
        createdAt: new Date(),
        size: buffer.length,
        dimensions: { width: 1200, height: 1600 }
      }
    };
  }

  // Helper methods for content generation
  private static async addInvoiceContent(
    doc: jsPDF,
    data: any,
    template: DocumentTemplate,
    branding: BrandingOptions
  ): Promise<void> {
    // Header with branding
    doc.setFontSize(24);
    doc.setTextColor(template.colorScheme.primary);
    doc.text('INVOICE', 20, 30);

    // Company info
    doc.setFontSize(12);
    doc.setTextColor(template.colorScheme.text);
    doc.text(branding.companyName, 20, 50);
    doc.text(branding.companyEmail, 20, 60);

    // Invoice details
    doc.text(`Invoice #: ${data.invoiceNumber || 'N/A'}`, 120, 50);
    doc.text(`Date: ${new Date(data.issueDate || Date.now()).toLocaleDateString()}`, 120, 60);
    doc.text(`Due: ${new Date(data.dueDate || Date.now()).toLocaleDateString()}`, 120, 70);

    // Customer info
    doc.text('Bill To:', 20, 90);
    doc.text(data.vendorName || 'Customer', 20, 100);

    // Line items table
    let yPos = 130;
    doc.text('Description', 20, yPos);
    doc.text('Qty', 100, yPos);
    doc.text('Price', 130, yPos);
    doc.text('Total', 160, yPos);

    // Add line items
    if (data.lineItems && data.lineItems.length > 0) {
      yPos += 10;
      data.lineItems.forEach((item: any) => {
        doc.text(item.description || '', 20, yPos);
        doc.text(String(item.quantity || 1), 100, yPos);
        doc.text(`$${item.unitPrice || 0}`, 130, yPos);
        doc.text(`$${item.totalPrice || 0}`, 160, yPos);
        yPos += 10;
      });
    }

    // Total
    yPos += 20;
    doc.setFontSize(14);
    doc.text(`Total: $${data.amount || 0}`, 120, yPos);
  }

  private static async addReportContent(
    doc: jsPDF,
    data: any,
    template: DocumentTemplate,
    branding: BrandingOptions
  ): Promise<void> {
    // Report header
    doc.setFontSize(20);
    doc.setTextColor(template.colorScheme.primary);
    doc.text(data.title || 'Financial Report', 20, 30);

    // Period
    doc.setFontSize(12);
    doc.setTextColor(template.colorScheme.text);
    doc.text(`Period: ${data.period || 'Current Month'}`, 20, 50);
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, 60);

    // Summary metrics
    let yPos = 80;
    if (data.summary) {
      doc.setFontSize(14);
      doc.text('Summary', 20, yPos);
      yPos += 15;

      Object.entries(data.summary).forEach(([key, value]) => {
        doc.setFontSize(10);
        doc.text(`${key}: ${value}`, 20, yPos);
        yPos += 10;
      });
    }
  }

  private static selectTemplateByContext(type: string, userProfile: any): string {
    // Simple template selection logic - can be enhanced with ML
    const templates = {
      invoice: userProfile.businessType === 'professional' ? 'modern-invoice' : 'classic-invoice',
      report: 'modern-report',
      contract: 'professional-contract'
    };

    return templates[type as keyof typeof templates] || 'default';
  }

  private static getTemplate(templateId: string): DocumentTemplate {
    // Return default template - in production, load from database
    return {
      id: templateId,
      name: 'Modern Template',
      type: 'modern',
      layout: 'modern',
      colorScheme: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#3b82f6',
        background: '#ffffff',
        text: '#1e293b',
        border: '#e2e8f0'
      },
      typography: {
        fontFamily: 'Inter',
        fontSize: {
          heading1: 24,
          heading2: 20,
          heading3: 16,
          body: 14,
          caption: 12
        },
        lineHeight: 1.5
      },
      components: []
    };
  }

  private static generateTitle(type: string, data: any): string {
    const titles = {
      invoice: `Invoice ${data.invoiceNumber || '#' + Date.now()}`,
      report: data.title || 'Financial Report',
      contract: data.title || 'Contract',
      proposal: data.title || 'Proposal'
    };

    return titles[type as keyof typeof titles] || 'Document';
  }

  private static async recordGeneration(
    userId: string,
    request: DocumentRequest,
    documents: GeneratedDocument[]
  ): Promise<void> {
    try {
      // Record in database for analytics
      await db.exportHistory.create({
        data: {
          userId,
          exportId: `doc-${Date.now()}`,
          fileName: this.generateTitle(request.type, request.data),
          fileUrl: '',
          format: request.format,
          count: documents.length,
          folderName: request.type
        }
      });
    } catch (error) {
      console.error('Failed to record document generation:', error);
    }
  }

  // Placeholder methods for canvas operations
  private static async addBrandingToCanvas(ctx: any, branding: BrandingOptions): Promise<void> {
    // Add company logo and branding to canvas
    ctx.fillStyle = branding.colors.primary;
    ctx.font = '24px Arial';
    ctx.fillText(branding.companyName, 50, 50);
  }

  private static async drawChart(ctx: any, data: any): Promise<void> {
    // Draw chart visualization
  }

  private static async drawInfographic(ctx: any, data: any): Promise<void> {
    // Draw infographic
  }

  private static async drawSummary(ctx: any, data: any): Promise<void> {
    // Draw summary visualization
  }

  private static async addDashboardCharts(worksheet: any, data: any): Promise<void> {
    // Add charts to Excel worksheet
  }

  private static async addSummaryTables(worksheet: any, data: any): Promise<void> {
    // Add summary tables
  }

  private static async addRawData(worksheet: any, data: any): Promise<void> {
    // Add raw data
  }

  private static async addReportSheets(workbook: any, data: any, template: any): Promise<void> {
    // Summary sheet
    const summarySheet = workbook.addWorksheet('Summary');
    summarySheet.getCell('A1').value = data.title || 'Financial Report';
    summarySheet.getCell('A1').font = { size: 18, bold: true };
    
    // Add summary data
    if (data.summary) {
      let row = 3;
      Object.entries(data.summary).forEach(([key, value]) => {
        summarySheet.getCell(`A${row}`).value = key;
        summarySheet.getCell(`B${row}`).value = value;
        row++;
      });
    }

    // Data sheet if invoices exist
    if (data.invoices && data.invoices.length > 0) {
      const dataSheet = workbook.addWorksheet('Invoice Data');
      
      // Headers
      const headers = ['Invoice #', 'Date', 'Vendor', 'Amount', 'Status'];
      const headerRow = dataSheet.getRow(1);
      headerRow.values = headers;
      headerRow.font = { bold: true };
      
      // Data rows
      data.invoices.forEach((invoice: any, index: number) => {
        const row = dataSheet.getRow(index + 2);
        row.values = [
          invoice.invoiceNumber || invoice.id,
          invoice.issueDate ? new Date(invoice.issueDate).toLocaleDateString() : '',
          invoice.vendorName || '',
          invoice.amount || 0,
          invoice.status || ''
        ];
      });

      // Auto-fit columns
      dataSheet.columns.forEach((col: any) => {
        col.width = 15;
      });
    }
  }

  private static async addInvoiceSheet(workbook: any, data: any, template: any): Promise<void> {
    const worksheet = workbook.addWorksheet('Invoice');
    
    // Header
    worksheet.getCell('A1').value = 'INVOICE';
    worksheet.getCell('A1').font = { size: 20, bold: true };
    worksheet.getCell('A1').fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: template.colorScheme.primary.replace('#', '') }
    };
    
    // Invoice details
    worksheet.getCell('A3').value = 'Invoice Number:';
    worksheet.getCell('B3').value = data.invoiceNumber || 'N/A';
    worksheet.getCell('A4').value = 'Date:';
    worksheet.getCell('B4').value = new Date(data.issueDate || Date.now()).toLocaleDateString();
    worksheet.getCell('A5').value = 'Due Date:';
    worksheet.getCell('B5').value = new Date(data.dueDate || Date.now()).toLocaleDateString();

    // Customer info
    worksheet.getCell('A7').value = 'Bill To:';
    worksheet.getCell('A8').value = data.vendorName || 'Customer';

    // Line items header
    const headerRow = worksheet.getRow(10);
    headerRow.values = ['Description', 'Quantity', 'Unit Price', 'Total'];
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'F0F0F0' }
    };

    // Add line items
    if (data.lineItems && data.lineItems.length > 0) {
      data.lineItems.forEach((item: any, index: number) => {
        const row = worksheet.getRow(11 + index);
        row.values = [
          item.description || '',
          item.quantity || 1,
          item.unitPrice || 0,
          item.totalPrice || 0
        ];
      });
    }

    // Total
    const totalRow = 11 + (data.lineItems?.length || 0) + 2;
    worksheet.getCell(`C${totalRow}`).value = 'TOTAL:';
    worksheet.getCell(`C${totalRow}`).font = { bold: true };
    worksheet.getCell(`D${totalRow}`).value = data.amount || 0;
    worksheet.getCell(`D${totalRow}`).font = { bold: true };

    // Auto-fit columns
    worksheet.columns.forEach((col: any) => {
      col.width = 15;
    });
  }

  private static async addAnalysisSheets(workbook: any, data: any, template: any): Promise<void> {
    // Add analysis sheets
  }

  private static async drawInvoiceImage(ctx: any, data: any, template: any): Promise<void> {
    // Draw invoice as image
  }

  private static async drawReportImage(ctx: any, data: any, template: any): Promise<void> {
    // Draw report as image
  }

  private static async generateInvoiceImage(
    data: any,
    template: DocumentTemplate,
    branding: BrandingOptions
  ): Promise<Buffer> {
    // For now, convert invoice to a simple text representation as PNG
    // In production, you would use Canvas API or a graphics library
    const invoiceText = `
INVOICE

Invoice #: ${data.invoiceNumber || 'N/A'}
Date: ${new Date(data.issueDate || Date.now()).toLocaleDateString()}
Due: ${new Date(data.dueDate || Date.now()).toLocaleDateString()}

Bill To: ${data.vendorName || 'Customer'}

${data.lineItems?.map((item: any) => 
  `${item.description} - Qty: ${item.quantity} - $${item.totalPrice || 0}`
).join('\n') || 'No items'}

Total: $${data.amount || 0}
`;

    // Create a simple buffer representation
    // In production, render this as actual PNG using canvas or image library
    return Buffer.from(invoiceText, 'utf8');
  }

  private static async addContractContent(doc: any, data: any, template: any, branding: any): Promise<void> {
    // Add contract content
    doc.setFontSize(20);
    doc.text(data.title || 'CONTRACT', 20, 30);
    
    doc.setFontSize(12);
    doc.text(`Date: ${new Date().toLocaleDateString()}`, 20, 50);
    doc.text(`Parties: ${data.parties || 'Not specified'}`, 20, 60);
    
    if (data.terms) {
      doc.text('Terms:', 20, 80);
      doc.text(data.terms, 20, 90);
    }
  }

  private static async addGenericContent(doc: any, data: any, template: any, branding: any): Promise<void> {
    // Add generic document content
    doc.setFontSize(20);
    doc.text(data.title || 'DOCUMENT', 20, 30);
    
    doc.setFontSize(12);
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, 50);
    
    if (data.content) {
      doc.text(data.content, 20, 70);
    }
  }

  // ===== ENHANCED CONTENT GENERATION METHODS =====

  /**
   * Add advanced invoice content to PDF
   */
  private static async addAdvancedInvoiceContent(
    doc: jsPDF,
    data: any,
    template: DocumentTemplate,
    branding: BrandingOptions,
    yPosition: number
  ): Promise<number> {
    const pageWidth = doc.internal.pageSize.getWidth();
    let currentY = yPosition;

    // Invoice title
    doc.setFontSize(template.typography.headingSize || 24);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(template.colorScheme.primary || '#000000');
    doc.text('INVOICE', 20, currentY);
    currentY += 15;

    // Invoice details
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor('#000000');

    doc.text('Invoice Number:', 20, currentY);
    doc.text(data.invoiceNumber || 'N/A', 80, currentY);
    currentY += 8;

    doc.text('Issue Date:', 20, currentY);
    doc.text(data.issueDate ? new Date(data.issueDate).toLocaleDateString() : 'N/A', 80, currentY);
    currentY += 8;

    doc.text('Due Date:', 20, currentY);
    doc.text(data.dueDate ? new Date(data.dueDate).toLocaleDateString() : 'N/A', 80, currentY);
    currentY += 15;

    // Vendor info
    if (data.vendor || data.vendorName) {
      doc.setFont('helvetica', 'bold');
      doc.text('Bill To:', 20, currentY);
      currentY += 8;

      doc.setFont('helvetica', 'normal');
      doc.text(data.vendorName || data.vendor?.name || 'N/A', 20, currentY);
      currentY += 15;
    }

    // Line items
    if (data.lineItems && data.lineItems.length > 0) {
      currentY = await this.addSimpleLineItemsTable(doc, data.lineItems, template, currentY);
    }

    // Total
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text('Total:', pageWidth - 80, currentY);
    doc.text(`$${(data.amount || data.total || 0).toFixed(2)}`, pageWidth - 30, currentY);

    return currentY + 20;
  }

  /**
   * Add advanced report content to PDF
   */
  private static async addAdvancedReportContent(
    doc: jsPDF,
    data: any,
    template: DocumentTemplate,
    branding: BrandingOptions,
    yPosition: number
  ): Promise<number> {
    let currentY = yPosition;

    // Report title
    doc.setFontSize(template.typography.headingSize || 20);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(template.colorScheme.primary || '#000000');
    doc.text(data.title || 'Financial Report', 20, currentY);
    currentY += 15;

    // Report period
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor('#666666');
    doc.text(`Period: ${data.period || 'Current Month'}`, 20, currentY);
    currentY += 20;

    // Summary data
    if (data.summary) {
      doc.setFont('helvetica', 'bold');
      doc.text('Summary:', 20, currentY);
      currentY += 10;

      doc.setFont('helvetica', 'normal');
      Object.entries(data.summary).forEach(([key, value]) => {
        doc.text(`${key}: ${value}`, 25, currentY);
        currentY += 8;
      });
    }

    return currentY + 20;
  }

  /**
   * Add analysis content to PDF
   */
  private static async addAnalysisContent(
    doc: jsPDF,
    data: any,
    template: DocumentTemplate,
    branding: BrandingOptions,
    yPosition: number
  ): Promise<number> {
    let currentY = yPosition;

    // Analysis title
    doc.setFontSize(template.typography.headingSize || 18);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(template.colorScheme.primary || '#000000');
    doc.text(data.title || 'Data Analysis', 20, currentY);
    currentY += 15;

    // Analysis content
    if (data.content) {
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor('#000000');

      const lines = doc.splitTextToSize(data.content, 170);
      doc.text(lines, 20, currentY);
      currentY += lines.length * 6;
    }

    return currentY + 20;
  }

  /**
   * Add simple line items table
   */
  private static async addSimpleLineItemsTable(
    doc: jsPDF,
    lineItems: any[],
    template: DocumentTemplate,
    yPosition: number
  ): Promise<number> {
    const pageWidth = doc.internal.pageSize.getWidth();
    let currentY = yPosition;

    // Table header
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    doc.text('Description', 25, currentY);
    doc.text('Qty', pageWidth - 80, currentY);
    doc.text('Price', pageWidth - 50, currentY);
    doc.text('Total', pageWidth - 25, currentY);
    currentY += 10;

    // Table rows
    doc.setFont('helvetica', 'normal');
    lineItems.forEach((item) => {
      doc.text(item.description || 'Item', 25, currentY);
      doc.text((item.quantity || 1).toString(), pageWidth - 80, currentY);
      doc.text(`$${(item.unitPrice || 0).toFixed(2)}`, pageWidth - 50, currentY);
      doc.text(`$${(item.totalPrice || item.amount || 0).toFixed(2)}`, pageWidth - 25, currentY);
      currentY += 8;
    });

    return currentY + 10;
  }
}
