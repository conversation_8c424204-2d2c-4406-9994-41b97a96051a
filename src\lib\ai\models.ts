import { groq } from '@ai-sdk/groq';
import { anthropic } from '@ai-sdk/anthropic';
import { customProvider } from 'ai';

export const DEFAULT_CHAT_MODEL: string = 'billix-smart';

// Intelligent model routing based on task complexity and requirements
export const myProvider = customProvider({
  languageModels: {
    // Primary chat model - Fast and intelligent
    'billix-smart': groq('llama-3.3-70b-versatile'), // Upgraded to Llama 3.3 70B for better performance

    // Specialized models for different tasks
    'billix-fast': groq('llama-3.1-8b-instant'), // Ultra-fast for simple queries
    'billix-reasoning': groq('deepseek-r1-distill-llama-70b'), // For complex analysis
    'billix-creative': groq('llama-3.3-70b-versatile'), // For creative tasks and document generation

    // Fallback models
    'claude-smart': anthropic('claude-3-5-haiku-********'), // Anthropic fallback
    'legacy-model': groq('meta-llama/llama-4-maverick-17b-128e-instruct'), // Legacy fallback
  },
});

/**
 * Intelligent model selection based on query type and complexity
 */
export function selectOptimalModel(query: string, context?: any): string {
  const lowerQuery = query.toLowerCase();
  const queryLength = query.length;
  const userExpertise = context?.user?.expertise || 'intermediate';
  const recentActions = context?.memory?.recentTopics || [];

  // Cost-performance optimization matrix
  const costMatrix = {
    'billix-fast': { cost: 1, performance: 0.7, speed: 1.0 },
    'billix-smart': { cost: 3, performance: 0.9, speed: 0.8 },
    'billix-reasoning': { cost: 5, performance: 1.0, speed: 0.6 },
    'billix-creative': { cost: 4, performance: 0.85, speed: 0.7 }
  };

  // For very simple queries or expert users who prefer speed
  if ((queryLength < 30) ||
      (userExpertise === 'expert' && queryLength < 100) ||
      lowerQuery.includes('hello') ||
      lowerQuery.includes('hi') ||
      lowerQuery.includes('thanks')) {
    return 'billix-fast';
  }

  // For complex reasoning tasks that justify the cost
  if (lowerQuery.includes('analyze') ||
      lowerQuery.includes('compare') ||
      lowerQuery.includes('calculate') ||
      lowerQuery.includes('forecast') ||
      lowerQuery.includes('predict') ||
      lowerQuery.includes('optimize')) {
    return 'billix-reasoning';
  }

  // For creative tasks (document generation, etc.)
  if (lowerQuery.includes('create') ||
      lowerQuery.includes('write') ||
      lowerQuery.includes('generate') ||
      lowerQuery.includes('draft') ||
      lowerQuery.includes('design') ||
      lowerQuery.includes('compose')) {
    return 'billix-creative';
  }

  // For repeated similar actions, use faster model
  if (recentActions.length > 0 &&
      recentActions.some(action => lowerQuery.includes(action.toLowerCase()))) {
    return 'billix-fast';
  }

  // Default to smart model for balanced performance/cost
  return 'billix-smart';
}

/**
 * Get model cost estimation per token
 */
export function getModelCostEstimate(model: string, tokenCount: number): number {
  const costPerToken = {
    'billix-fast': 0.0001,      // Very cheap - Llama 3.1 8B
    'billix-smart': 0.0003,     // Moderate - Llama 3.3 70B
    'billix-reasoning': 0.0005, // Expensive - DeepSeek R1
    'billix-creative': 0.0004   // Creative premium - Llama 3.3 70B
  };

  return (costPerToken[model as keyof typeof costPerToken] || 0.0003) * tokenCount;
}

/**
 * Smart model routing with cost limits
 */
export function getModelWithCostLimit(
  primaryModel: string,
  tokenCount: number,
  maxCost: number = 0.1
): string {
  const estimatedCost = getModelCostEstimate(primaryModel, tokenCount);

  if (estimatedCost > maxCost) {
    // Fallback to cheaper model
    if (primaryModel === 'billix-reasoning') return 'billix-smart';
    if (primaryModel === 'billix-creative') return 'billix-smart';
    if (primaryModel === 'billix-smart') return 'billix-fast';
  }

  return primaryModel;
}

/**
 * Analyze query complexity for better model selection
 */
export function analyzeQueryComplexity(query: string): {
  score: number;
  requiresReasoning: boolean;
  isCreative: boolean;
  estimatedTokens: number;
} {
  const lowerQuery = query.toLowerCase();
  let score = 0;

  // Length factor
  score += Math.min(query.length / 500, 0.3);

  // Complexity keywords
  const complexKeywords = ['analyze', 'compare', 'calculate', 'optimize', 'predict', 'forecast'];
  const creativeKeywords = ['create', 'generate', 'write', 'design', 'compose', 'draft'];
  const reasoningKeywords = ['why', 'how', 'explain', 'because', 'therefore', 'however'];

  complexKeywords.forEach(keyword => {
    if (lowerQuery.includes(keyword)) score += 0.2;
  });

  reasoningKeywords.forEach(keyword => {
    if (lowerQuery.includes(keyword)) score += 0.15;
  });

  const requiresReasoning = complexKeywords.some(k => lowerQuery.includes(k)) ||
                           reasoningKeywords.some(k => lowerQuery.includes(k));

  const isCreative = creativeKeywords.some(k => lowerQuery.includes(k));

  // Estimate tokens (rough approximation)
  const estimatedTokens = Math.ceil(query.length / 4);

  return {
    score: Math.min(score, 1),
    requiresReasoning,
    isCreative,
    estimatedTokens
  };
}

interface ChatModel {
  id: string;
  name: string;
  description: string;
}

export const chatModels: Array<ChatModel> = [
  {
    id: 'billix-chat',
    name: 'Billix AI',
    description: 'Advanced AI assistant for financial management',
  },
];
