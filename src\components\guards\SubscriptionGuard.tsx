'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';

interface SubscriptionStatus {
  hasActiveSubscription: boolean;
  subscription?: {
    id: number;
    status: string;
  } | null;
}

interface SubscriptionGuardProps {
  children: React.ReactNode;
}

export default function SubscriptionGuard({ children }: SubscriptionGuardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const checkSubscription = async () => {
      try {
        // Skip subscription check for subscription-related pages
        const isSubscriptionPage = pathname === '/dashboard/subscription';
        const hasSuccessParams = searchParams.get('success') === 'true' || searchParams.get('session_id');
        const isConfirmationPage = pathname === '/confirmation';
        
        // If we're on subscription page, confirmation page, or have success params, allow access
        if (isSubscriptionPage || hasSuccessParams || isConfirmationPage) {
          setHasAccess(true);
          setIsLoading(false);
          return;
        }

        // Add a small delay for potential webhook processing
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check subscription status with retry logic
        let retries = 3;
        let isActive = false;
        const sessionId = searchParams.get('session_id');
        
        while (retries > 0 && !isActive) {
          try {
            const response = await fetch('/api/subscriptions/active', {
              cache: 'no-cache',
              headers: {
                'Cache-Control': 'no-cache',
              },
            });
            
            if (!response.ok) {
              throw new Error('Failed to check subscription');
            }

            const data = await response.json();

            // The API returns either { isActive: true } or a full subscription object
            isActive = data.isActive !== undefined ? data.isActive : (data.status === 'active');
            
            // If still not active and we have a session ID, try manual activation
            if (!isActive && sessionId && retries === 3) {
              console.log('Attempting manual subscription activation with session:', sessionId);
              try {
                const activateResponse = await fetch('/api/subscriptions/activate', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ sessionId }),
                });
                
                const activateResult = await activateResponse.json();
                console.log('Manual activation result:', activateResult);
                
                if (activateResult.success) {
                  isActive = true;
                }
              } catch (activateError) {
                console.error('Manual activation failed:', activateError);
              }
            }
            
            if (!isActive && retries > 1) {
              // Wait a bit longer before retrying to allow webhook processing
              await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            retries--;
          } catch (error) {
            console.error(`Subscription check attempt failed (${4 - retries}/3):`, error);
            retries--;
            
            if (retries > 0) {
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        }

        if (isActive) {
          setHasAccess(true);
        } else {
          // Redirect to pricing with return URL
          const returnUrl = encodeURIComponent(pathname);
          router.push(`/pricing?return_url=${returnUrl}`);
          return;
        }
      } catch (error) {
        console.error('Subscription check failed:', error);
        // On error, allow access for subscription-related pages, otherwise redirect
        const isSubscriptionRelated = pathname === '/dashboard/subscription' || 
                                    pathname === '/confirmation' ||
                                    searchParams.get('success') === 'true';
        
        if (isSubscriptionRelated) {
          setHasAccess(true);
        } else {
          const returnUrl = encodeURIComponent(pathname);
          router.push(`/pricing?return_url=${returnUrl}`);
          return;
        }
      } finally {
        setIsLoading(false);
      }
    };

    checkSubscription();
  }, [pathname, router, searchParams]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background dark:bg-[#0B1739]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Checking access...</p>
        </div>
      </div>
    );
  }

  if (!hasAccess) {
    return null; // Component will redirect, so don't render anything
  }

  return <>{children}</>;
}
