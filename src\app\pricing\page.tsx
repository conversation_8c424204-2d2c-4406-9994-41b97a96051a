import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { checkSubscriptionStatusWithUserId } from '@/lib/subscription-cache';
import PricingPageComponent from '@/components/pricing/PricingPage';

interface PricingPageProps {
  searchParams?: Promise<{
    return_url?: string;
    success?: string;
    session_id?: string;
  }>;
}

export default async function PricingPage({
  searchParams,
}: PricingPageProps) {
  const params = await searchParams;

  // Don't redirect if user is coming from a successful checkout or has a session_id
  // This prevents the redirect loop when checkout completes
  const isFromSuccessfulCheckout =
    params?.success === 'true' || params?.session_id;

  if (!isFromSuccessfulCheckout) {
    try {
      // Get auth info safely
      const authResult = await auth();
      
      if (authResult.userId) {
        // Check if user already has an active subscription
        const subscriptionStatus = await checkSubscriptionStatusWithUserId(authResult.userId);

        if (subscriptionStatus.hasActiveSubscription) {
          // User already has a subscription, redirect to dashboard or return URL
          const returnUrl = params?.return_url;
          redirect(returnUrl || '/dashboard');
        }
      }
    } catch (error) {
      // If subscription check fails, continue to pricing page
      console.error('Subscription check failed:', error);
    }
  }

  // Get return URL for after successful purchase
  const returnUrl = params?.return_url;

  return (
    <div className="min-h-screen bg-black">
      <PricingPageComponent returnUrl={returnUrl} />
    </div>
  );
}
