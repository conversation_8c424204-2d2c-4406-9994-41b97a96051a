'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { SearchIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

export function Search() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const router = useRouter();

  const handleSearch = () => {
    if (searchQuery.trim()) {
      // Navigate to invoices page with search parameter
      router.push(
        `/dashboard/invoices?search=${encodeURIComponent(searchQuery.trim())}`
      );
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="relative min-w-0 flex-1 sm:flex-initial">
      <div className={cn(
        'relative flex items-center transition-all duration-300 ease-out',
        'hover:scale-105 hover:-translate-y-1',
        'group'
      )}>
        {/* Enhanced search icon with gradient background */}
        <div className={cn(
          'absolute left-3 z-10 p-2 rounded-xl transition-all duration-300',
          'bg-gradient-to-br from-gray-100/90 to-slate-100/90',
          'dark:from-gray-800/60 dark:to-slate-800/60',
          'shadow-sm group-hover:shadow-md',
          isFocused && [
            'from-blue-100/90 to-indigo-100/90',
            'dark:from-blue-900/60 dark:to-indigo-900/60',
            'shadow-lg shadow-blue-500/20'
          ]
        )}>
          <SearchIcon className={cn(
            'h-4 w-4 transition-all duration-300',
            isFocused ? [
              'text-blue-600 dark:text-blue-400',
              'scale-110'
            ] : 'text-gray-500 dark:text-gray-400',
            'group-hover:scale-105'
          )} />
        </div>

        {/* Enhanced input field */}
        <Input
          type="search"
          placeholder="Search invoices, vendors, amounts..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={cn(
            'h-12 w-full pl-14 pr-4',
            'sm:w-[280px] md:w-[320px] lg:w-[360px]',
            // Enhanced base styling
            'font-medium rounded-xl border-2',
            // Enhanced background gradients
            'bg-gradient-to-r from-white via-blue-50/40 to-indigo-50/40',
            'dark:from-gray-800/90 dark:via-blue-950/40 dark:to-indigo-950/40',
            // Enhanced borders with better contrast
            'border-blue-200/60 dark:border-blue-700/60',
            'hover:border-blue-400/80 dark:hover:border-blue-500/80',
            'focus:border-blue-500/90 dark:focus:border-blue-400/90',
            // Enhanced shadow effects
            'shadow-xl shadow-blue-500/10 dark:shadow-blue-400/5',
            'hover:shadow-2xl hover:shadow-blue-500/20 dark:hover:shadow-blue-400/10',
            'focus:shadow-2xl focus:shadow-blue-500/30 dark:focus:shadow-blue-400/15',
            // Smooth transition effects
            'transition-all duration-300 ease-out',
            // Enhanced text styling
            'text-gray-800 dark:text-gray-200',
            'placeholder:text-gray-500 dark:placeholder:text-gray-400',
            'placeholder:font-normal',
            // Enhanced focus states
            'focus-visible:ring-3 focus-visible:ring-blue-500/60 focus-visible:ring-offset-3',
            'focus-visible:ring-offset-background',
            // Backdrop blur effect
            'backdrop-blur-sm'
          )}
        />

        {/* Enhanced search indicator */}
        {searchQuery && (
          <div className="absolute right-3 flex items-center space-x-2">
            <div className="px-3 py-1.5 rounded-lg bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/40 dark:to-indigo-900/40 shadow-lg">
              <span className="text-xs font-bold text-blue-700 dark:text-blue-300">
                {searchQuery.length}
              </span>
            </div>
            {searchQuery.length > 0 && (
              <button
                onClick={() => setSearchQuery('')}
                className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                <svg className="w-3 h-3 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Search suggestions hint */}
        {!searchQuery && isFocused && (
          <div className="absolute top-full left-0 right-0 mt-2 p-3 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-blue-200/50 dark:border-blue-700/50 z-50">
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <div className="font-medium mb-1">Search suggestions:</div>
              <div>• Invoice numbers, vendor names</div>
              <div>• Amount ranges (e.g.,1000)</div>
              <div>• Date ranges and categories</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
