import type { Prisma } from "@prisma/client"
import ExcelJS from "exceljs"

type JsonValue = Prisma.JsonValue

interface ExtractedData {
  notes?: string
  vendor?: {
    address?: string
    email?: string
    phone?: string
  }
  customer?: {
    name?: string
    address?: string
  }
  financials?: {
    subtotal?: string
    tax?: string
    shipping?: string
    discount?: string
    total?: string
  }
  termsAndConditions?: string
  meta?: {
    language?: string
    languageName?: string
    confidence?: {
      overall?: number
    }
  }
  [key: string]: unknown
}

interface LineItem {
  description?: string
  quantity?: number
  unitPrice?: number
  totalPrice?: number
  taxRate?: number | null
  taxAmount?: number | null
  discount?: number | null
  productSku?: string | null
  notes?: string | null
  attributes?: Record<string, unknown>
  [key: string]: unknown
}

interface InvoiceWithExtras {
  id: string
  invoiceNumber?: string | null
  status?: string
  amount?: number | null
  currency?: string | null
  issueDate?: Date | null
  dueDate?: Date | null
  vendorName?: string | null
  notes?: string | null
  createdAt: Date
  updatedAt: Date
  category?: { name: string; color?: string } | null
  categoryId?: string | null
  extractedData?: JsonValue
  lineItems?: LineItem[]
}


// Helper function to format notes properly
function formatNotes(notes: string): string {
  if (!notes) return ""

  if (typeof notes === "string") {
    try {
      // Try to parse as JSON to see if it's a JSON string
      const parsed = JSON.parse(notes)
      return typeof parsed === "object" ? JSON.stringify(parsed, null, 2) : notes
    } catch {
      return notes
    }
  } else if (typeof notes === "object") {
    return JSON.stringify(notes, null, 2)
  }

  return String(notes)
}

// Helper function to format dates in a clean format (keeping numbers in English)
function formatDate(date: Date | string): string {
  if (!date) return "N/A";

  try {
    const dateObj = date instanceof Date ? date : new Date(date);

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) return "N/A";

    // Format to YYYY-MM-DD only, without time
    return dateObj.toISOString().split('T')[0];
  } catch {
    return String(date);
  }
}

// Helper function to normalize currency codes
function normalizeCurrency(currency: string | null | undefined): string {
  if (!currency) return "USD";

  // Common currency symbols to ISO codes mapping
  const symbolToCode: Record<string, string> = {
    "$": "USD",
    "€": "EUR",
    "£": "GBP",
    "¥": "JPY",
    "₹": "INR",
    "₽": "RUB",
    "₩": "KRW",
    "₪": "ILS", // Israeli Shekel
    "₺": "TRY", // Turkish Lira
    "₴": "UAH", // Ukrainian Hryvnia
    "₦": "NGN", // Nigerian Naira
    "₱": "PHP", // Philippine Peso
    "฿": "THB", // Thai Baht
    "₫": "VND", // Vietnamese Dong
    "₲": "PYG", // Paraguayan Guarani
    "₡": "CRC", // Costa Rican Colón
    "₼": "AZN", // Azerbaijani Manat
    "₾": "GEL", // Georgian Lari
  };

  // If it's a symbol, convert to code
  if (currency.length === 1 && symbolToCode[currency]) {
    return symbolToCode[currency];
  }

  // If it's already a valid 3-letter code, return it
  if (currency.length === 3 && /^[A-Z]{3}$/.test(currency)) {
    return currency;
  }

  // For any other case, default to USD
  return "USD";
}

// Helper function to format currency
function formatCurrency(amount: number, currency = "USD"): string {
  // Normalize the currency code
  const normalizedCurrency = normalizeCurrency(currency);

  try {
    // Always use en-US locale to keep numbers in English format
    return new Intl.NumberFormat('en-US', {
      style: "currency",
      currency: normalizedCurrency,
    }).format(amount);
  } catch {
    // Fallback to basic formatting if Intl.NumberFormat fails
    return `${normalizedCurrency} ${amount.toFixed(2)}`;
  }
}

export async function generateExcel(invoices: InvoiceWithExtras[], fields: string[]): Promise<Buffer> {
  // Create a new workbook
  const workbook = new ExcelJS.Workbook()
  workbook.creator = "Invoice Management System"
  workbook.created = new Date()

  // Add a worksheet for invoices
  const worksheet = workbook.addWorksheet("Invoices", {
    properties: { tabColor: { argb: "4F81BD" } },
  })

  // Define the columns based on selected fields
  const columns: Partial<ExcelJS.Column>[] = []

  if (fields.includes("invoiceNumber")) {
    columns.push({ header: "Invoice Number", key: "invoiceNumber", width: 15 })
  }

  if (fields.includes("date")) {
    columns.push({ header: "Issue Date", key: "issueDate", width: 15 })
  }

  if (fields.includes("dueDate")) {
    columns.push({ header: "Due Date", key: "dueDate", width: 15 })
  }

  if (fields.includes("vendor")) {
    columns.push({ header: "Vendor", key: "vendor", width: 20 })
  }

  if (fields.includes("amount")) {
    columns.push({ header: "Amount", key: "amount", width: 15 })
  }

  if (fields.includes("status")) {
    columns.push({ header: "Status", key: "status", width: 12 })
  }

  if (fields.includes("category")) {
    columns.push({ header: "Category", key: "category", width: 15 })
  }

  if (fields.includes("notes")) {
    columns.push({ header: "Notes", key: "notes", width: 40 })
  }

  // Add columns to the worksheet
  worksheet.columns = columns

  // Add rows
  invoices.forEach((invoice) => {
    // Detect if the invoice is in Arabic
    const extractedData =
      invoice.extractedData && typeof invoice.extractedData === "object"
        ? (invoice.extractedData as unknown as ExtractedData)
        : undefined

    const isArabic = extractedData?.meta?.language === "ar"

    const row: Record<string, string | number | boolean | null | undefined> = {}

    if (fields.includes("invoiceNumber")) {
      row.invoiceNumber = invoice.invoiceNumber || "N/A"
    }

    if (fields.includes("date")) {
      row.issueDate = invoice.issueDate ? formatDate(invoice.issueDate) : "N/A"
    }

    if (fields.includes("dueDate")) {
      row.dueDate = invoice.dueDate ? formatDate(invoice.dueDate) : "N/A"
    }

    if (fields.includes("vendor")) {
      row.vendor = invoice.vendorName || "Unknown Vendor"
    }

    if (fields.includes("amount")) {
      row.amount = invoice.amount ? formatCurrency(invoice.amount, invoice.currency || "USD") : "N/A"
    }

    if (fields.includes("status")) {
      row.status = invoice.status
    }

    if (fields.includes("category")) {
      row.category = invoice.category ? invoice.category.name : "Uncategorized"
    }

    if (fields.includes("notes")) {
      const rawNotes = invoice.notes || extractedData?.notes
      row.notes = formatNotes(rawNotes || "")
    }

    const newRow = worksheet.addRow(row)

    // Set RTL alignment for Arabic text
    if (isArabic) {
      newRow.eachCell((cell) => {
        if (cell && cell.value) {
          cell.alignment = {
            ...cell.alignment,
            readingOrder: 'rtl',
            horizontal: 'right'
          }
        }
      })
    }
  })

  // Add styling
  const headerRow = worksheet.getRow(1)
  headerRow.font = { bold: true, color: { argb: "FFFFFF" } }
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "4F81BD" },
  }
  headerRow.alignment = { vertical: "middle", horizontal: "center" }

  // Apply borders to all cells
  worksheet.eachRow({ includeEmpty: false }, (row) => {
    row.eachCell({ includeEmpty: false }, (cell) => {
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      }
    })
  })

  // If line items are included, add a separate worksheet for them
  if (fields.includes("lineItems")) {
    const lineItemsSheet = workbook.addWorksheet("Line Items", {
      properties: { tabColor: { argb: "92D050" } },
    })

    // Define standard columns for line items
    const standardColumns = [
      { header: "Invoice Number", key: "invoiceNumber", width: 15 },
      { header: "Description", key: "description", width: 30 },
      { header: "Quantity", key: "quantity", width: 10 },
      { header: "Unit Price", key: "unitPrice", width: 15 },
      { header: "Total Price", key: "totalPrice", width: 15 },
      { header: "Tax Rate", key: "taxRate", width: 10 },
      { header: "Tax Amount", key: "taxAmount", width: 15 },
      { header: "Discount", key: "discount", width: 10 },
      { header: "SKU", key: "sku", width: 15 },
    ]

    // Collect all unique attribute keys from all line items
    const allAttributeKeys = new Set<string>()

    invoices.forEach((invoice) => {
      if (invoice.lineItems && Array.isArray(invoice.lineItems)) {
        invoice.lineItems.forEach((item) => {
          // Add attributes from the attributes object
          if (item.attributes && typeof item.attributes === "object") {
            Object.keys(item.attributes).forEach((key) => allAttributeKeys.add(key))
          }

          // Also check for other non-standard fields
          Object.keys(item).forEach((key) => {
            if (
              ![
                "id",
                "description",
                "quantity",
                "unitPrice",
                "totalPrice",
                "taxRate",
                "taxAmount",
                "discount",
                "productSku",
                "notes",
                "attributes",
                "invoiceId",
              ].includes(key)
            ) {
              allAttributeKeys.add(key)
            }
          })
        })
      }
    })

    // Add attribute columns to the standard columns
    const attributeColumns = Array.from(allAttributeKeys).map((key) => ({
      header: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, " $1"),
      key,
      width: 15,
    }))

    // Set all columns
    lineItemsSheet.columns = [...standardColumns, ...attributeColumns]

    // Add line items from all invoices
    let rowIndex = 2 // Start after header
    invoices.forEach((invoice) => {
      // Detect if the invoice is in Arabic
      const extractedData =
        invoice.extractedData && typeof invoice.extractedData === "object"
          ? (invoice.extractedData as unknown as ExtractedData)
          : undefined

      const isArabic = extractedData?.meta?.language === "ar"

      if (invoice.lineItems && Array.isArray(invoice.lineItems) && invoice.lineItems.length > 0) {
        invoice.lineItems.forEach((item: LineItem) => {
          const rowData: Record<string, string | number | boolean | null | undefined> = {
            invoiceNumber: invoice.invoiceNumber || invoice.id,
            description: item.description || "N/A",
            quantity: item.quantity || "N/A",
            unitPrice: item.unitPrice ? formatCurrency(item.unitPrice, invoice.currency || "USD") : "N/A",
            totalPrice: item.totalPrice ? formatCurrency(item.totalPrice, invoice.currency || "USD") : "N/A",
            taxRate:
              item.taxRate !== null && item.taxRate !== undefined ? `${(item.taxRate * 100).toFixed(2)}%` : "N/A",
            taxAmount:
              item.taxAmount !== null && item.taxAmount !== undefined
                ? formatCurrency(item.taxAmount, invoice.currency || "USD")
                : "N/A",
            discount:
              item.discount !== null && item.discount !== undefined
                ? typeof item.discount === "number" && item.discount <= 1
                  ? `${(item.discount * 100).toFixed(0)}%`
                  : formatCurrency(Number(item.discount), invoice.currency || "USD")
                : "N/A",
            sku: item.productSku || "N/A",
          }

          // Add attribute values
          allAttributeKeys.forEach((key) => {
            // Check if the attribute is in the attributes object
            if (item.attributes && key in item.attributes) {
              const value = item.attributes[key]

              // Format date values to only show the date portion
              if (key === 'createdAt' || key === 'updatedAt' || key === 'date') {
                if (value && typeof value === 'string' && value.includes('T')) {
                  rowData[key] = value.split('T')[0];
                } else {
                  rowData[key] = value !== null && typeof value === "object" ? JSON.stringify(value) : String(value);
                }
              } else {
                rowData[key] = value !== null && typeof value === "object" ? JSON.stringify(value) : String(value);
              }
            }
            // Check if it's a direct property of the line item
            else if (key in item) {
              const value = item[key as keyof typeof item]

              // Format date values to only show the date portion
              if (key === 'createdAt' || key === 'updatedAt' || key === 'date') {
                if (value && typeof value === 'string' && value.includes('T')) {
                  rowData[key] = String(value).split('T')[0];
                } else {
                  rowData[key] = value !== null && typeof value === "object" ? JSON.stringify(value) : String(value);
                }
              } else {
                rowData[key] = value !== null && typeof value === "object" ? JSON.stringify(value) : String(value);
              }
            } else {
              rowData[key] = "N/A"
            }
          })

          const newRow = lineItemsSheet.addRow(rowData)

          // Set RTL alignment for Arabic text
          if (isArabic) {
            newRow.eachCell((cell) => {
              if (cell && cell.value) {
                cell.alignment = {
                  ...cell.alignment,
                  readingOrder: 'rtl',
                  horizontal: 'right'
                }
              }
            })
          }

          // Add alternating row colors
          if (rowIndex % 2 === 0) {
            lineItemsSheet.getRow(rowIndex).fill = {
              type: "pattern",
              pattern: "solid",
              fgColor: { argb: "F2F2F2" },
            }
          }
          rowIndex++
        })
      }
    })

    // Add styling to line items sheet
    const lineItemsHeader = lineItemsSheet.getRow(1)
    lineItemsHeader.font = { bold: true, color: { argb: "FFFFFF" } }
    lineItemsHeader.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "92D050" },
    }
    lineItemsHeader.alignment = { vertical: "middle", horizontal: "center" }

    // Apply borders to all cells
    lineItemsSheet.eachRow({ includeEmpty: false }, (row) => {
      row.eachCell({ includeEmpty: false }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        }
      })
    })
  }

  // If metadata is included, add a separate worksheet for it
  if (fields.includes("metadata")) {
    const metadataSheet = workbook.addWorksheet("Metadata", {
      properties: { tabColor: { argb: "C0504D" } },
    })

    // Define columns for metadata
    metadataSheet.columns = [
      { header: "Invoice Number", key: "invoiceNumber", width: 15 },
      { header: "Created At", key: "createdAt", width: 20 },
      { header: "Updated At", key: "updatedAt", width: 20 },
      { header: "ID", key: "id", width: 40 },
      { header: "Language", key: "language", width: 15 },
      { header: "Confidence", key: "confidence", width: 15 },
    ]

    // Add metadata for all invoices
    let rowIndex = 2 // Start after header
    invoices.forEach((invoice) => {
      let language = "N/A"
      let confidence = "N/A"

      // Detect if the invoice is in Arabic
      const extractedData = invoice.extractedData && typeof invoice.extractedData === "object"
        ? (invoice.extractedData as unknown as ExtractedData)
        : undefined

      const isArabic = extractedData?.meta?.language === "ar"

      if (extractedData?.meta?.language) {
        language = extractedData.meta.languageName || extractedData.meta.language
      }
      if (extractedData?.meta?.confidence?.overall !== undefined) {
        confidence = `${extractedData.meta.confidence.overall}%`
      }

      const newRow = metadataSheet.addRow({
        invoiceNumber: invoice.invoiceNumber || invoice.id,
        createdAt: new Date(invoice.createdAt).toLocaleString('en-US'),
        updatedAt: new Date(invoice.updatedAt).toLocaleString('en-US'),
        id: invoice.id,
        language,
        confidence,
      })

      // Set RTL alignment for Arabic text
      if (isArabic) {
        newRow.eachCell((cell) => {
          if (cell && cell.value) {
            cell.alignment = {
              ...cell.alignment,
              readingOrder: 'rtl',
              horizontal: 'right'
            }
          }
        })
      }

      // Add alternating row colors
      if (rowIndex % 2 === 0) {
        metadataSheet.getRow(rowIndex).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "F2F2F2" },
        }
      }
      rowIndex++
    })

    // Add styling to metadata sheet
    const metadataHeader = metadataSheet.getRow(1)
    metadataHeader.font = { bold: true, color: { argb: "FFFFFF" } }
    metadataHeader.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "C0504D" },
    }
    metadataHeader.alignment = { vertical: "middle", horizontal: "center" }

    // Apply borders to all cells
    metadataSheet.eachRow({ includeEmpty: false }, (row) => {
      row.eachCell({ includeEmpty: false }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        }
      })
    })
  }

  // Add a dynamic data worksheet if there are any custom fields
  const allCustomFields = new Set<string>()
  invoices.forEach((invoice) => {
    if (invoice.extractedData && typeof invoice.extractedData === "object") {
      const extractedData = invoice.extractedData as unknown as ExtractedData
      // Find all custom fields that aren't in standard sections
      Object.keys(extractedData).forEach((key) => {
        if (!["vendor", "customer", "financials", "meta", "notes", "termsAndConditions"].includes(key)) {
          allCustomFields.add(key)
        }
      })
    }
  })

  if (allCustomFields.size > 0 && fields.includes("dynamicData")) {
    const dynamicSheet = workbook.addWorksheet("Custom Fields", {
      properties: { tabColor: { argb: "9966FF" } },
    })

    // Create columns - invoice number plus all custom fields
    const dynamicColumns: Partial<ExcelJS.Column>[] = [{ header: "Invoice Number", key: "invoiceNumber", width: 15 }]

    allCustomFields.forEach((field) => {
      dynamicColumns.push({
        header: field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, " $1"),
        key: field,
        width: 20,
      })
    })

    dynamicSheet.columns = dynamicColumns

    // Add rows for each invoice
    let rowIndex = 2 // Start after header
    invoices.forEach((invoice) => {
      // Detect if the invoice is in Arabic
      const extractedData = invoice.extractedData && typeof invoice.extractedData === "object"
        ? (invoice.extractedData as unknown as ExtractedData)
        : undefined

      const isArabic = extractedData?.meta?.language === "ar"

      const row: Record<string, string | number | boolean | null | undefined | object> = {
        invoiceNumber: invoice.invoiceNumber || invoice.id,
      }

      if (extractedData) {
        allCustomFields.forEach((field) => {
          if (extractedData[field]) {
            if (typeof extractedData[field] === "object") {
              // For object values, convert to a string representation
              row[field] = JSON.stringify(extractedData[field])
            } else {
              row[field] = extractedData[field]
            }
          } else {
            row[field] = "N/A"
          }
        })
      }

      const newRow = dynamicSheet.addRow(row)

      // Set RTL alignment for Arabic text
      if (isArabic) {
        newRow.eachCell((cell) => {
          if (cell && cell.value) {
            cell.alignment = {
              ...cell.alignment,
              readingOrder: 'rtl',
              horizontal: 'right'
            }
          }
        })
      }

      // Add alternating row colors
      if (rowIndex % 2 === 0) {
        dynamicSheet.getRow(rowIndex).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "F2F2F2" },
        }
      }
      rowIndex++
    })

    // Add styling to dynamic sheet
    const dynamicHeader = dynamicSheet.getRow(1)
    dynamicHeader.font = { bold: true, color: { argb: "FFFFFF" } }
    dynamicHeader.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "9966FF" },
    }
    dynamicHeader.alignment = { vertical: "middle", horizontal: "center" }

    // Apply borders to all cells
    dynamicSheet.eachRow({ includeEmpty: false }, (row) => {
      row.eachCell({ includeEmpty: false }, (cell) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        }
      })
    })
  }

  // Write to buffer
  return (await workbook.xlsx.writeBuffer()) as unknown as Buffer
}
