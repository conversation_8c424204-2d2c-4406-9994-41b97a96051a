'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Pencil, Trash2, Check, X, Plus, Copy } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, SheetClose } from '@/components/ui/sheet';
import { DatePicker } from '@/components/ui/date-picker';

type ApiKey = {
  users_api_key_id: string;
  name: string;
  api_key: string;
  created_at?: string;
  last_used_at?: string;
  expires_at?: string;
  usage_24h?: number;
  chat_usage?: number;
  invoice_usage?: number;
  is_active?: boolean;
  active?: boolean;
};

type DbUser = {
  id: string;
  [key: string]: unknown;
};

type ApiKeyResponse = {
  users_api_key_id: string;
  name: string;
  api_key: string;
  created_at?: string;
  last_used_at?: string;
  expires_at?: string;
  is_active?: boolean;
  api_usages?: Array<{
    chatUsage?: number;
    invoiceUsage?: number;
  }>;
};

const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
if (!backendUrl) {
  throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not configured');
}

const ApiKeysTab: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [apiKeysLoading, setApiKeysLoading] = useState(false);
  const [apiKeysError, setApiKeysError] = useState<string | null>(null);
  const [apiKeyName, setApiKeyName] = useState("");
  const [search, setSearch] = useState("");
  const [editKeyId, setEditKeyId] = useState<string | null>(null);
  const [editKeyName, setEditKeyName] = useState("");
  const [toggleLoading, setToggleLoading] = useState<string | null>(null);
  const [dbUser, setDbUser] = useState<DbUser | null>(null);
  const [userLoading, setUserLoading] = useState(true);
  const [openApiSheet, setOpenApiSheet] = useState(false);
  const [expiresDate, setExpiresDate] = useState<Date | undefined>(() => {
    const d = new Date();
    d.setDate(d.getDate() + 30);
    return d;
  });

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('API key copied to clipboard.');
    } catch {
      toast.error('Failed to copy API key.');
    }
  };

  const handleViewApiKeys = useCallback(async () => {
    if (!dbUser?.id) return;
    setApiKeysLoading(true);
    setApiKeysError(null);
    setApiKeys([]);
    try {
      const res = await fetch(`${backendUrl}/api/v1/api-keys/user/${dbUser.id}`);
      if (!res.ok) throw new Error('Failed to fetch API keys');
      const data = await res.json();
      const mapped = Array.isArray(data)
        ? (data as ApiKeyResponse[]).map((k) => {
            const usage = Array.isArray(k.api_usages) && k.api_usages.length > 0 ? k.api_usages[0] : {};
            return {
              ...k,
              chat_usage: usage.chatUsage ?? 0,
              invoice_usage: usage.invoiceUsage ?? 0,
              active: !!k.is_active,
            };
          })
        : [];
      setApiKeys(mapped);
    } catch (err: unknown) {
      setApiKeysError(err instanceof Error ? err.message : 'Error fetching API keys');
    } finally {
      setApiKeysLoading(false);
    }
  }, [dbUser?.id]);

  useEffect(() => {
    async function fetchDbUser() {
      setUserLoading(true);
      try {
        const res = await fetch('/api/user/profile');
        if (!res.ok) throw new Error('Failed to fetch user');
        const data = await res.json();
        setDbUser(data);
      } catch {
        setDbUser(null);
      } finally {
        setUserLoading(false);
      }
    }
    fetchDbUser();
  }, []);

  useEffect(() => {
    if (dbUser) handleViewApiKeys();
  }, [dbUser, handleViewApiKeys]);

  const handleDeleteApiKey = async (key: ApiKey) => {
    if (!key?.api_key) return;
    try {
      const res = await fetch(`${backendUrl}/api/v1/api-keys/${key.api_key}`, {
        method: 'DELETE',
      });
      if (!res.ok) throw new Error('Failed to delete API key');
      toast.success(`${key.name || 'API Key'} deleted successfully.`);
      handleViewApiKeys();
    } catch (err: unknown) {
      toast.error(err instanceof Error ? err.message : 'Failed to delete API key.');
    }
  };

  const handleToggleActive = async (key: ApiKey) => {
    setToggleLoading(key.users_api_key_id);
    setApiKeys(prev => prev.map(k => k.users_api_key_id === key.users_api_key_id ? { ...k, active: !k.active } : k));
    try {
      const url = `${backendUrl}/api/v1/api-keys/${key.api_key}/toggle`;
      const res = await fetch(url, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
      });
      if (!res.ok) {
        throw new Error('Failed to toggle API key');
      }
    } catch (err: unknown) {
      setApiKeys(prev => prev.map(k => k.users_api_key_id === key.users_api_key_id ? { ...k, active: key.active } : k));
      toast.error(err instanceof Error ? err.message : 'Failed to toggle API key.');
    } finally {
      setToggleLoading(null);
    }
  };

  const handleEditKeyName = async (key: ApiKey) => {
    try {
      const res = await fetch(`${backendUrl}/api/v1/api-keys/${key.api_key}/name`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: editKeyName }),
      });
      if (!res.ok) throw new Error('Failed to update API key name');
      setApiKeys(prev => prev.map(k => k.users_api_key_id === key.users_api_key_id ? { ...k, name: editKeyName } : k));
      setEditKeyId(null);
      setEditKeyName("");
      toast.success('API key name updated successfully.');
    } catch (err: unknown) {
      toast.error(err instanceof Error ? err.message : 'Failed to update API key name.');
    }
  };

  const handleApiKeySave = async () => {
    if (!dbUser?.id) {
      toast.error('Cannot create API key without user ID.');
      return;
    }
    if (!apiKeyName.trim()) {
      toast.error('Please enter a name for your API key.');
      return;
    }
    setIsLoading(true);
    const expiresAt = expiresDate ? expiresDate.toISOString() : undefined;
    const payload = { user_id: dbUser.id, expires_at: expiresAt, name: apiKeyName };
    try {
      const response = await fetch(`${backendUrl}/api/v1/api-keys`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!response.ok) throw new Error('Failed to create API key');
      toast.success('Your new API key has been generated.');
      setExpiresDate(() => {
        const d = new Date();
        d.setDate(d.getDate() + 30);
        return d;
      });
      setApiKeyName("");
      handleViewApiKeys();
      setOpenApiSheet(false); 
    } catch {
      toast.error('Failed to create API key.');
    } finally {
      setIsLoading(false);
    }
  };

  if (userLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>API Keys</CardTitle>
          <CardDescription>
            Manage your API keys and access.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex gap-2 mb-4">
            <Input
              placeholder="Search your API Keys..."
              value={search}
              onChange={e => setSearch(e.target.value)}
              className="flex-1"
            />
            <Sheet open={openApiSheet} onOpenChange={setOpenApiSheet}>
              <SheetTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setOpenApiSheet(true)}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Create API Key
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="max-w-md w-full">
                <SheetHeader>
                  <SheetTitle>Create New API Key</SheetTitle>
                </SheetHeader>
                <div className="p-4 pt-2 flex flex-col gap-2">
                  <Label htmlFor="api-key-name">API Key Name</Label>
                  <Input
                    id="api-key-name"
                    placeholder="API Key Name"
                    value={apiKeyName}
                    onChange={e => setApiKeyName(e.target.value)}
                    className="flex-1"
                  />
                  <Label className="mt-2">Expiration Date</Label>
                  <DatePicker date={expiresDate} setDate={setExpiresDate} />
                </div>
                <SheetFooter className="flex flex-row justify-between gap-2 mt-0 pt-0">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleApiKeySave}
                    disabled={isLoading}
                  >
                    Create
                  </Button>
                  <SheetClose asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setOpenApiSheet(false)}
                    >
                      Cancel
                    </Button>
                  </SheetClose>
                </SheetFooter>
              </SheetContent>
            </Sheet>
          </div>
          {apiKeysLoading && <div>Loading...</div>}
          {apiKeysError && <div className="text-red-500">{apiKeysError}</div>}
          {!apiKeysLoading && !apiKeysError && (
            <div className="h-96 overflow-y-auto mb-2 scrollbar-thin scrollbar-thumb-blue-200 dark:scrollbar-thumb-blue-900">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-semibold">User API Keys</div>
              </div>
              <div className="w-full overflow-x-auto">
                <table className="min-w-full text-sm border-separate border-spacing-y-2">
                  <thead>
                    <tr className="text-left text-muted-foreground">
                      <th className="px-3 py-2 font-medium">NAME</th>
                      <th className="px-3 py-2 font-medium">SECRET KEY</th>
                      <th className="px-3 py-2 font-medium">CREATED</th>
                      <th className="px-3 py-2 font-medium">LAST USED</th>
                      <th className="px-3 py-2 font-medium">USAGE (24HRS)</th>
                      <th className="px-3 py-2 font-medium">Chat Usage</th>
                      <th className="px-3 py-2 font-medium">Invoice Usage</th>
                      <th className="px-3 py-2 font-medium"> </th>
                    </tr>
                  </thead>
                  <tbody>
                    {apiKeys.filter(key => key.name?.toLowerCase().includes(search.toLowerCase())).map((key: ApiKey) => (
                      <tr key={key.users_api_key_id} className="bg-card rounded-md shadow-sm">
                        <td className="px-3 py-2 min-w-[140px]">
                          {editKeyId === key.users_api_key_id ? (
                            <div className="flex items-center gap-2">
                              <Input
                                value={editKeyName}
                                onChange={e => setEditKeyName(e.target.value)}
                                className="h-7 text-xs px-2"
                                autoFocus
                              />
                              <Button size="icon" variant="ghost" className="h-7 w-7" onClick={() => handleEditKeyName(key)}>
                                <Check className="w-4 h-4 text-green-600" />
                              </Button>
                              <Button size="icon" variant="ghost" className="h-7 w-7" onClick={() => { setEditKeyId(null); setEditKeyName(""); }}>
                                <X className="w-4 h-4 text-red-500" />
                              </Button>
                            </div>
                          ) : (
                            <span className="font-medium truncate text-sm">{key.name || 'Unnamed'}</span>
                          )}
                        </td>
                        <td className="px-3 py-2 min-w-[120px]">
                          {key.api_key ? (
                            <button
                              onClick={() => copyToClipboard(key.api_key)}
                              className="font-mono hover:bg-muted px-2 py-1 rounded cursor-pointer flex items-center gap-1 transition-colors"
                              title="Click to copy API key"
                            >
                              <span>{key.api_key.slice(0, 8)}...{key.api_key.slice(-4)}</span>
                              <Copy className="w-3 h-3 opacity-60" />
                            </button>
                          ) : (
                            <span className="font-mono">Hidden</span>
                          )}
                        </td>
                        <td className="px-3 py-2 min-w-[110px]">
                          {key.created_at ? new Date(key.created_at).toLocaleDateString() : '-'}
                        </td>
                        <td className="px-3 py-2 min-w-[110px]">
                          {key.expires_at ? new Date(key.expires_at).toLocaleDateString() : '-'}
                        </td>
                        <td className="px-3 py-2 min-w-[110px]">
                          {(() => {
                            const chatUsage = key.chat_usage === 0 ? 0 : (key.chat_usage ?? '-');
                            const invoiceUsage = key.invoice_usage === 0 ? 0 : (key.invoice_usage ?? '-');
                            
                            if (chatUsage === '-' || invoiceUsage === '-') {
                              return '-';
                            }
                            
                            const totalUsage = chatUsage + invoiceUsage;
                            return  totalUsage ;
                          })()}
                        </td>
                        <td className="px-3 py-2 min-w-[110px]">
                          {key.chat_usage === 0 ? 0 : (key.chat_usage ?? '-')}
                        </td>
                        <td className="px-3 py-2 min-w-[110px]">
                          {key.invoice_usage === 0 ? 0 : (key.invoice_usage ?? '-')}
                        </td>
                        <td className="px-3 py-2 min-w-[120px]">
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={!!key.active}
                              onCheckedChange={() => handleToggleActive(key)}
                              disabled={toggleLoading === key.users_api_key_id}
                              className="data-[state=checked]:bg-[#343388] data-[state=unchecked]:bg-[#E5E7EB] border-2 border-[#343388]"
                            />
                            <Button size="icon" variant="ghost" className="h-7 w-7" onClick={() => { setEditKeyId(key.users_api_key_id); setEditKeyName(key.name || ''); }}>
                              <Pencil className="w-4 h-4" />
                            </Button>
                            <Button size="icon" variant="ghost" className="h-7 w-7" onClick={() => handleDeleteApiKey(key)}>
                              <Trash2 className="w-4 h-4 text-red-500" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiKeysTab; 