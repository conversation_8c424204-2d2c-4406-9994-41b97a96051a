"use client";

import { ChangeEvent } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";

interface BasicInfoTabProps {
  basicData: {
    invoiceNumber: string;
    status: string;
    issueDate: Date | null;
    dueDate: Date | null;
    amount: string;
    currency: string;
    vendorName: string;
    notes: string;
    categoryId: string;
  };
  categories: { id: string; name: string }[];
  handleBasicInputChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleBasicSelectChange: (name: string, value: string) => void;
  handleBasicDateChange: (name: string, date: Date | null) => void;
}

export function BasicInfoTab({
  basicData,
  categories,
  handleBasicInputChange,
  handleBasicSelectChange,
  handleBasicDateChange,
}: BasicInfoTabProps) {
  return (
    <div className="space-y-4">
      {/* Basic invoice information */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="invoiceNumber">Invoice Number</Label>
          <Input
            id="invoiceNumber"
            name="invoiceNumber"
            value={basicData.invoiceNumber}
            onChange={handleBasicInputChange}
            placeholder="INV-001"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            value={basicData.status}
            onValueChange={(value) => handleBasicSelectChange("status", value)}
          >
            <SelectTrigger id="status">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="PAID">Paid</SelectItem>
              <SelectItem value="OVERDUE">Overdue</SelectItem>
              <SelectItem value="CANCELLED">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Issue Date</Label>
          <DatePicker
            date={basicData.issueDate || undefined}
            onDateChange={(date) => handleBasicDateChange("issueDate", date || null)}
            placeholder="Pick issue date"
          />
        </div>
        <div className="space-y-2">
          <Label>Due Date</Label>
          <DatePicker
            date={basicData.dueDate || undefined}
            onDateChange={(date) => handleBasicDateChange("dueDate", date || null)}
            placeholder="Pick due date"
            disablePastDates={true}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="amount">Amount</Label>
          <Input
            id="amount"
            name="amount"
            type="number"
            step="0.01"
            value={basicData.amount}
            onChange={handleBasicInputChange}
            placeholder="0.00"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="currency">Currency</Label>
          <Select
            value={basicData.currency}
            onValueChange={(value) => handleBasicSelectChange("currency", value)}
          >
            <SelectTrigger id="currency">
              <SelectValue placeholder="Select currency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USD">USD</SelectItem>
              <SelectItem value="EUR">EUR</SelectItem>
              <SelectItem value="GBP">GBP</SelectItem>
              <SelectItem value="ILS">ILS</SelectItem>
              <SelectItem value="JOD">JOD</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="vendorName">Vendor Name</Label>
          <Input
            id="vendorName"
            name="vendorName"
            value={basicData.vendorName}
            onChange={handleBasicInputChange}
            placeholder="Vendor name"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select
            value={basicData.categoryId || "uncategorized"}
            onValueChange={(value) => handleBasicSelectChange("categoryId", value === "uncategorized" ? "" : value)}
          >
            <SelectTrigger id="category">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="uncategorized">Uncategorized</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          name="notes"
          value={basicData.notes}
          onChange={handleBasicInputChange}
          placeholder="Add notes about this invoice"
          rows={3}
        />
      </div>
    </div>
  );
}
