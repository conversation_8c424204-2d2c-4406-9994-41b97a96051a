'use server';

import { auth } from '@clerk/nextjs/server';
import db from '@/db/db';
import type { InvoiceStatus, Prisma } from '@prisma/client';
import { nanoid } from 'nanoid';
import { generatePdf } from '@/lib/actions/pdf-generator-ar';
import { put } from '@vercel/blob';

interface GetInvoicesParams {
  page?: number;
  limit?: number;
  status?: string;
  vendor?: string;
  category?: string;
  dateRange?: string;
  sort?: string;
  order?: string;
  search?: string;
}

export async function getInvoices({
  page = 1,
  limit = 10,
  status,
  vendor,
  category,
  dateRange,
  sort = 'createdAt',
  order = 'desc',
  search = '',
}: GetInvoicesParams) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Ensure reasonable limits to prevent performance issues
    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100); // Cap at 100 items per page

    // Build the where clause for filtering
    const where: Prisma.InvoiceWhereInput = {
      userId: user.id,
    };

    // Add status filter if provided
    if (status) {
      where.status = status as InvoiceStatus;
    }

    // Add vendor filter if provided
    if (vendor) {
      where.vendorId = vendor;
    }

    // Add category filter if provided
    if (category) {
      where.categoryId = category;
    }

    // Add date range filter if provided
    if (dateRange) {
      const [startDate, endDate] = dateRange.split(',');
      if (startDate && endDate) {
        where.issueDate = {
          gte: new Date(startDate),
          lte: new Date(endDate),
        };
      } else if (startDate) {
        where.issueDate = {
          gte: new Date(startDate),
        };
      }
    }

    // Add search filter if provided
    if (search) {
      where.OR = [
        { invoiceNumber: { contains: search, mode: 'insensitive' } },
        { vendorName: { contains: search, mode: 'insensitive' } },
        { title: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Calculate pagination
    const skip = (safePage - 1) * safeLimit;

    // Get total count for pagination
    const totalCount = await db.invoice.count({ where });

    // Calculate page count
    const pageCount = Math.ceil(totalCount / safeLimit);

    // Get invoices with pagination, sorting, and filtering
    // Optimized query - removed heavy fields for better performance
    const invoices = await db.invoice.findMany({
      where,
      select: {
        id: true,
        invoiceNumber: true,
        status: true,
        issueDate: true,
        dueDate: true,
        amount: true,
        currency: true,
        category: {
          select: {
            id: true,
            name: true,
            color: true,
          },
        },
        vendorName: true,
        vendor: {
          select: {
            id: true,
            name: true,
          },
        },
        thumbnailUrl: true,
        originalFileUrl: true,
        // Removed heavy fields for list view performance:
        // - notes: only needed in detail view
        // - extractedData: can be very large, only needed in detail view
        // - lineItems: only needed in detail view
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        [sort]: order,
      },
      skip,
      take: safeLimit,
    });

    return {
      success: true,
      invoices,
      totalCount,
      pageCount,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function getInvoiceById(id: string) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get invoice with related data
    const invoice = await db.invoice.findFirst({
      where: {
        id,
        userId: user.id,
      },
      include: {
        category: true,
        vendor: true,
        lineItems: true,
      },
    });

    return {
      success: true,
      invoice,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function updateInvoiceStatus(
  id: string,
  status: InvoiceStatus
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Update invoice status
    const updatedInvoice = await db.invoice.update({
      where: {
        id,
        userId: user.id,
      },
      data: {
        status,
      },
    });

    return {
      success: true,
      data: updatedInvoice,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function deleteInvoice(id: string) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Delete invoice
    await db.invoice.delete({
      where: {
        id,
        userId: user.id,
      },
    });

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function generateInvoicePdf(
  id: string,
  fields: string[]
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return { success: false, error: 'Unauthorized' };
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    // Get the invoice
    const invoice = await db.invoice.findFirst({
      where: {
        id,
        userId: user.id,
      },
      include: {
        category: true,
        vendor: true,
        lineItems: true,
      },
    });

    if (!invoice) {
      return { success: false, error: 'Invoice not found' };
    }

    // Generate a unique export ID
    const exportId = nanoid();

    // Generate the PDF
    const mappedInvoice = {
      ...invoice,
      category: invoice.category
        ? {
            name: invoice.category.name,
            color: invoice.category.color || undefined,
          }
        : null,
      lineItems: invoice.lineItems.map((item) => ({
        ...item,
        attributes: item.attributes
          ? (item.attributes as Record<string, unknown>)
          : undefined,
      })),
    };

    const pdfBuffer = await generatePdf([mappedInvoice], fields);

    // Create a filename
    const fileName = `invoice_${invoice.invoiceNumber || invoice.id}_${exportId}.pdf`;

    // Upload the file to Vercel Blob
    const { url } = await put(`exports/${fileName}`, pdfBuffer, {
      contentType: 'application/pdf',
      access: 'public',
    });

    return {
      success: true,
      downloadUrl: url,
      fileName,
    };
  } catch (error) {
    console.error('Error generating PDF:', error);
    return {
      success: false,
      error: 'Failed to generate PDF',
    };
  }
}

export async function updateInvoiceMetadata(
  id: string,
  data: {
    selectedCategory?: string | null;
    selectedVendorType?: string | null;
  }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      throw new Error('Unauthorized');
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get the existing invoice to access the extractedData
    const existingInvoice = await db.invoice.findFirst({
      where: {
        id,
        userId: user.id,
      },
      select: {
        extractedData: true,
      },
    });

    if (!existingInvoice) {
      throw new Error('Invoice not found');
    }

    // Update the extractedData with the new metadata
    const extractedData = existingInvoice.extractedData as Record<
      string,
      unknown
    >;

    // Ensure meta and suggestions objects exist
    if (!extractedData.meta) extractedData.meta = {};
    if (
      typeof extractedData.meta === 'object' &&
      !Array.isArray(extractedData.meta)
    ) {
      const meta = extractedData.meta as Record<string, unknown>;
      if (!meta.suggestions) meta.suggestions = {};

      if (
        typeof meta.suggestions === 'object' &&
        !Array.isArray(meta.suggestions)
      ) {
        const suggestions = meta.suggestions as Record<
          string,
          unknown
        >;

        // Update the selected values
        if (data.selectedCategory !== undefined) {
          suggestions.selectedCategory = data.selectedCategory;
        }

        if (data.selectedVendorType !== undefined) {
          suggestions.selectedVendorType = data.selectedVendorType;
        }
      }
    }

    // Update database operations
    const updateData: Prisma.InvoiceUpdateInput = {
      extractedData: extractedData as Prisma.InputJsonValue,
    };

    // If selectedCategory is provided and not null, connect to or create category
    if (data.selectedCategory) {
      // Try to find existing category
      const existingCategory = await db.category.findFirst({
        where: {
          name: data.selectedCategory,
          userId: user.id,
        },
      });

      if (existingCategory) {
        // Connect to existing category
        updateData.category = {
          connect: { id: existingCategory.id },
        };
      } else {
        // Create new category with random color
        const colors = [
          '#4CAF50',
          '#2196F3',
          '#F44336',
          '#FF9800',
          '#9C27B0',
          '#607D8B',
        ];
        const randomColor =
          colors[Math.floor(Math.random() * colors.length)];

        const newCategory = await db.category.create({
          data: {
            name: data.selectedCategory,
            color: randomColor,
            userId: user.id,
          },
        });

        updateData.category = {
          connect: { id: newCategory.id },
        };
      }
    }

    // If selectedVendorType is provided and starts with "Vendor: ", extract vendor name and connect/create
    if (
      data.selectedVendorType &&
      data.selectedVendorType.startsWith('Vendor: ')
    ) {
      const vendorName = data.selectedVendorType.substring(8).trim();

      if (vendorName) {
        // Try to find existing vendor
        const existingVendor = await db.vendor.findFirst({
          where: {
            name: vendorName,
            userId: user.id,
          },
        });

        if (existingVendor) {
          // Connect to existing vendor
          updateData.vendor = {
            connect: { id: existingVendor.id },
          };
        } else {
          // Create new vendor
          const newVendor = await db.vendor.create({
            data: {
              name: vendorName,
              userId: user.id,
            },
          });

          updateData.vendor = {
            connect: { id: newVendor.id },
          };
        }
      }
    }

    // Update the invoice
    const updatedInvoice = await db.invoice.update({
      where: {
        id,
        userId: user.id,
      },
      data: updateData,
    });

    return {
      success: true,
      data: updatedInvoice,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Function to update multiple invoices with a category
export async function updateMultipleInvoicesCategory(
  invoiceIds: string[],
  categoryName: string
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return { success: false, error: 'Unauthorized' };
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    // Find or create the category
    let category;
    try {
      // Try to find existing category
      category = await db.category.findFirst({
        where: {
          name: categoryName,
          userId: user.id,
        },
      });

      if (!category) {
        // Create new category with random color
        const colors = [
          '#4CAF50',
          '#2196F3',
          '#F44336',
          '#FF9800',
          '#9C27B0',
          '#607D8B',
        ];
        const randomColor =
          colors[Math.floor(Math.random() * colors.length)];

        category = await db.category.create({
          data: {
            name: categoryName,
            color: randomColor,
            userId: user.id,
          },
        });
      }
    } catch (error) {
      console.error('Error updating multiple invoices category:', error);
      return {
        success: false,
        error: 'Failed to find or create category',
      };
    }

    // Update all invoices with the category
    const updateResults = [];
    let failedCount = 0;

    for (const invoiceId of invoiceIds) {
      try {
        // Get the existing invoice to access the extractedData
        const existingInvoice = await db.invoice.findFirst({
          where: {
            id: invoiceId,
            userId: user.id,
          },
          select: {
            extractedData: true,
          },
        });

        if (!existingInvoice) {
          failedCount++;
          continue;
        }

        // Update the extractedData with the new category
        const extractedData = existingInvoice.extractedData as Record<
          string,
          unknown
        >;

        // Ensure meta and suggestions objects exist
        if (!extractedData.meta) extractedData.meta = {};
        if (
          typeof extractedData.meta === 'object' &&
          !Array.isArray(extractedData.meta)
        ) {
          const meta = extractedData.meta as Record<string, unknown>;
          if (!meta.suggestions) meta.suggestions = {};

          if (
            typeof meta.suggestions === 'object' &&
            !Array.isArray(meta.suggestions)
          ) {
            const suggestions = meta.suggestions as Record<
              string,
              unknown
            >;

            // Update the selected category
            suggestions.selectedCategory = categoryName;
          }
        }

        // Update the invoice
        const updatedInvoice = await db.invoice.update({
          where: {
            id: invoiceId,
            userId: user.id,
          },
          data: {
            extractedData: extractedData as Prisma.InputJsonValue,
            category: {
              connect: { id: category.id },
            },
          },
        });

        updateResults.push({
          id: updatedInvoice.id,
          success: true,
        });
      } catch (error) {
        failedCount++;
        console.error('Error updating invoice:', error);
      }
    }

    return {
      success: true,
      updatedCount: updateResults.length,
      failedCount,
      categoryId: category.id,
      categoryName: category.name,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Update an invoice with new data
export async function updateInvoice(
  id: string,
  data: {
    invoiceNumber?: string | null;
    status?: InvoiceStatus;
    issueDate?: Date | null;
    dueDate?: Date | null;
    amount?: number | null;
    currency?: string | null;
    vendorName?: string | null;
    notes?: string | null;
    categoryId?: string | null;
    lineItems?: Array<{
      id?: string;
      description?: string;
      quantity?: number;
      unitPrice?: number;
      totalPrice?: number;
      taxRate?: number | null;
      taxAmount?: number | null;
      discount?: number | null;
      productSku?: string | null;
      notes?: string | null;
      attributes?: Record<string, unknown>;
    }>;
    extractedData?: Record<string, unknown>;
  }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return { success: false, error: 'Unauthorized' };
    }

    // Get the database user ID from Clerk ID
    const user = await db.user.findUnique({
      where: { clerkId: userId },
      select: { id: true },
    });

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    // Check if the invoice exists and belongs to the user
    const existingInvoice = await db.invoice.findFirst({
      where: {
        id,
        userId: user.id,
      },
    });

    if (!existingInvoice) {
      return { success: false, error: 'Invoice not found' };
    }

    // Prepare update data
    const updateData: Prisma.InvoiceUpdateInput = {};

    // Only include fields that are provided
    if (data.invoiceNumber !== undefined) {
      updateData.invoiceNumber = data.invoiceNumber;
    }

    if (data.status !== undefined) {
      updateData.status = data.status;
    }

    if (data.issueDate !== undefined) {
      updateData.issueDate = data.issueDate;
    }

    if (data.dueDate !== undefined) {
      updateData.dueDate = data.dueDate;
    }

    if (data.amount !== undefined) {
      updateData.amount = data.amount;
    }

    if (data.currency !== undefined) {
      updateData.currency = data.currency;
    }

    if (data.vendorName !== undefined) {
      updateData.vendorName = data.vendorName;
    }

    if (data.notes !== undefined) {
      updateData.notes = data.notes;
    }

    // Handle category connection/disconnection
    if (data.categoryId !== undefined) {
      if (data.categoryId) {
        // Connect to category
        updateData.category = {
          connect: { id: data.categoryId },
        };
      } else {
        // Disconnect from category
        updateData.category = {
          disconnect: true,
        };
      }
    }

    // Handle line items update if provided
    if (data.lineItems) {
      // Delete existing line items
      await db.invoiceLineItem.deleteMany({
        where: {
          invoiceId: id,
        },
      });

      // Create new line items
      updateData.lineItems = {
        create: data.lineItems.map((item) => ({
          description: item.description || '',
          quantity: item.quantity || 0,
          unitPrice: item.unitPrice || 0,
          totalPrice: item.totalPrice || 0,
          taxRate: item.taxRate,
          taxAmount: item.taxAmount,
          discount: item.discount,
          productSku: item.productSku,
          notes: item.notes,
          attributes: item.attributes as Prisma.InputJsonValue,
        })),
      };
    }

    // Handle extracted data update if provided
    if (data.extractedData) {
      updateData.extractedData =
        data.extractedData as Prisma.InputJsonValue;
    }

    // Update the invoice
    const updatedInvoice = await db.invoice.update({
      where: {
        id,
        userId: user.id,
      },
      data: updateData,
      include: {
        category: true,
        lineItems: true,
      },
    });

    return {
      success: true,
      data: updatedInvoice,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
