import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FilePlus, Download, Settings, BarChart3 } from "lucide-react";
import Link from "next/link";
import UpdateInvoiceTypesButton from "@/app/dashboard/reports/update-invoice-types";

interface ReportHeaderProps {
  activeTab: string;
}

export function ReportHeader({ activeTab }: ReportHeaderProps) {
  const getTitle = () => {
    switch (activeTab) {
      case "templates":
        return "Report Templates";
      case "scheduled":
        return "Scheduled Reports";
      case "custom":
        return "Custom Reports";
      default:
        return "Reports Overview";
    }
  };

  const getIcon = () => {
    switch (activeTab) {
      case "templates":
        return <BarChart3 className="h-5 w-5" />;
      case "scheduled":
        return <Download className="h-5 w-5" />;
      case "custom":
        return <Settings className="h-5 w-5" />;
      default:
        return <BarChart3 className="h-5 w-5" />;
    }
  };

  const getDescription = () => {
    switch (activeTab) {
      case "templates":
        return "Pre-built report templates for quick insights";
      case "scheduled":
        return "Automated reports delivered on schedule";
      case "custom":
        return "Build tailored reports for specific needs";
      default:
        return "Comprehensive business intelligence and analytics";
    }
  };

  return (
    <header className="sticky top-0 z-10 border-b bg-background/80 backdrop-blur-sm">
      <div className="flex h-20 items-center justify-between px-6">
        <div className="flex items-center gap-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg">
            {getIcon()}
          </div>
          <div className="space-y-1">
            <h1 className="text-2xl font-bold tracking-tight">
              {getTitle()}
            </h1>
            <p className="text-sm text-muted-foreground">
              {getDescription()}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Badge 
            variant="outline" 
            className="hidden lg:flex items-center gap-1 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/50 dark:to-emerald-950/50 border-green-200 dark:border-green-800/50"
          >
            <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
            <span className="text-green-700 dark:text-green-400">System Active</span>
          </Badge>
          
          <UpdateInvoiceTypesButton />
          
          <Link href="/dashboard/reports/new">
            <Button 
              size="sm" 
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 dark:from-blue-500 dark:to-purple-500 dark:hover:from-blue-600 dark:hover:to-purple-600 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <FilePlus className="mr-2 h-4 w-4" />
              Create Report
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
}
