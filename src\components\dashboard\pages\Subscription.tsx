'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Tabs,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Check,
  CreditCard,
  Calendar,
  DollarSign,
  FileText,
  MessageSquare,
  AlertTriangle,
  Star,
  Shield,
} from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { handleUpgrade } from '@/actions/subscription-actions';
import { format } from 'date-fns';
import { toast } from 'sonner';

interface SubscriptionProps {
  userSubscriptions: {
    id: number;
    paddleSubscriptionId?: string | null;
    provider?: string;
    orderId?: number | null;
    name: string;
    email: string;
    status: string;
    statusFormatted: string;
    renewsAt: string | null;
    endsAt: string | null;
    trialEndsAt: string | null;
    price: string;
    isPaused: boolean | null;
    subscriptionItemId: string | number;
    isUsageBased: boolean | null;
    userId: string;
    planId: number;
    cancelUrl?: string | null;
    updateUrl?: string | null;
  }[];
  plans: {
    id: number;
    name: string;
    description: string | null;
    price: string;
    interval: string | null;
    intervalCount: number | null;
    isUsageBased: boolean | null;
    productId: number;
    productName: string | null;
    variantId: number;
    paddlePriceId: string | null;
    trialInterval: string | null;
    trialIntervalCount: number | null;
    sort: number | null;
  }[];
  successParams?: {
    sessionId?: string;
    returnUrl?: string;
  };
}

interface UsageStats {
  chatUsage: number;
  invoiceUsage: number;
  chatLimit: number;
  invoiceLimit: number;
  resetDate: Date;
  daysUntilReset: number;
}

interface BillingTransaction {
  description?: string;
  date?: string;
  amount?: string;
  status?: string;
}

type UserSubscription = SubscriptionProps['userSubscriptions'][0];

// Type guard to check if a subscription exists and is active
function isActiveSubscription(
  subscription: UserSubscription | null | undefined
): subscription is UserSubscription {
  return (
    subscription !== null &&
    subscription !== undefined &&
    subscription.status === 'active'
  );
}

function formatPrice(price: string | number) {
  const cents = typeof price === 'string' ? parseInt(price) : price;
  return `$${(cents / 100).toFixed(2)}`;
}

const Subscription = ({
  userSubscriptions,
  plans,
  successParams,
}: SubscriptionProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [portalLoading, setPortalLoading] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);
  const [billingHistory, setBillingHistory] = useState<BillingTransaction[]>([]);
  const [billingLoading, setBillingLoading] = useState(false);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(
    null
  );
  const [usageLoading, setUsageLoading] = useState(true);
  const [limitExceededInfo, setLimitExceededInfo] = useState<{
    feature: string;
    message: string;
  } | null>(null);

  // Handle success redirect with client-side navigation
  useEffect(() => {
    if (successParams?.returnUrl) {
      // Show success message
      toast.success('Subscription activated successfully!');

      // Try to activate subscription manually in case webhook is delayed
      const activateSubscription = async () => {
        if (successParams.sessionId) {
          try {
            const response = await fetch('/api/subscriptions/activate', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                sessionId: successParams.sessionId,
              }),
            });

            const result = await response.json();
            console.log('Subscription activation result:', result);
          } catch (error) {
            console.error('Error activating subscription:', error);
          }
        }
      };

      // Activate subscription immediately
      activateSubscription();

      // Small delay to ensure webhook has processed, then redirect
      const timer = setTimeout(() => {
        const returnUrl = decodeURIComponent(successParams.returnUrl!);
        console.log('Redirecting to return URL:', returnUrl);
        
        // Force a hard navigation to ensure proper state refresh
        window.location.href = returnUrl;
      }, 3000); // Increased delay to allow webhook processing

      return () => clearTimeout(timer);
    }
  }, [successParams]);

  // Handle usage limit exceeded messages from URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const limitExceeded = urlParams.get('limit_exceeded');
    const message = urlParams.get('message');

    if (limitExceeded && message) {
      setLimitExceededInfo({
        feature: limitExceeded,
        message: decodeURIComponent(message),
      });

      toast.error(decodeURIComponent(message), {
        duration: 5000,
        action: {
          label: 'Upgrade Now',
          onClick: () => {
            // Scroll to plans section
            const plansTab =
              document.querySelector('[value="plans"]');
            if (plansTab) {
              (plansTab as HTMLElement).click();
            }
          },
        },
      });

      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('limit_exceeded');
      newUrl.searchParams.delete('message');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, []);

  // Fetch billing history when component mounts
  useEffect(() => {
    const fetchBillingHistory = async () => {
      setBillingLoading(true);
      try {
        const response = await fetch('/api/billing/history');
        if (response.ok) {
          const result = await response.json();
          setBillingHistory(result.data || []);
        }
      } catch (error) {
        console.error('Error fetching billing history:', error);
      } finally {
        setBillingLoading(false);
      }
    };

    fetchBillingHistory();
  }, []);

  // Fetch usage stats when component mounts
  useEffect(() => {
    const fetchUsageStats = async () => {
      setUsageLoading(true);
      try {
        const response = await fetch('/api/usage/stats');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setUsageStats({
              ...result.stats,
              resetDate: new Date(result.stats.resetDate),
            });
          }
        }
      } catch (error) {
        console.error('Error fetching usage stats:', error);
      } finally {
        setUsageLoading(false);
      }
    };

    fetchUsageStats();
  }, []);

  // Get the active subscription if any
  const activeSubscription =
    userSubscriptions.find((sub) => sub.status === 'active') || null;

  // Calculate usage percentages
  const documentsPercentage =
    usageStats && usageStats.invoiceLimit > 0
      ? (usageStats.invoiceUsage / usageStats.invoiceLimit) * 100
      : 0;

  const chatPercentage =
    usageStats && usageStats.chatLimit > 0
      ? (usageStats.chatUsage / usageStats.chatLimit) * 100
      : 0;

  // Get the current plan details
  const currentPlan =
    activeSubscription && activeSubscription.planId
      ? plans.find((plan) => plan.id === activeSubscription.planId)
      : null;

  // Format next billing date
  const nextBillingDate = activeSubscription?.renewsAt
    ? format(new Date(activeSubscription.renewsAt), 'MMMM dd, yyyy')
    : 'N/A';

  // Function to handle plan cancellation based on provider
  const handlePlanCancel = async () => {
    if (!isActiveSubscription(activeSubscription)) return;

    try {
      setCancelLoading(true);

      if (
        activeSubscription.provider === 'paddle' &&
        activeSubscription.paddleSubscriptionId
      ) {
        const response = await fetch(
          '/api/paddle/cancel-subscription',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              subscriptionId: activeSubscription.paddleSubscriptionId,
            }),
          }
        );

        const result = await response.json();

        if (result.success) {
          toast.success('Subscription canceled successfully');
          window.location.reload();
        } else {
          toast.error(
            result.error || 'Failed to cancel subscription'
          );
        }
      } else {
        toast.error(
          'Unable to identify subscription provider - only Paddle subscriptions are supported'
        );
      }
    } catch {
      toast.error('Failed to cancel subscription');
    } finally {
      setCancelLoading(false);
    }
  };

  // Function to handle customer portal access based on provider
  const handleCustomerPortal = async () => {
    if (!isActiveSubscription(activeSubscription)) return;

    try {
      setPortalLoading(true);

      if (
        activeSubscription.provider === 'paddle' &&
        activeSubscription.paddleSubscriptionId
      ) {
        const response = await fetch('/api/paddle/customer-portal', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            subscriptionId: activeSubscription.paddleSubscriptionId,
          }),
        });

        const result = await response.json();

        if (result.success && result.data?.update_payment_method) {
          window.open(result.data.update_payment_method, '_blank');
        } else {
          toast.error(
            result.error || 'Unable to access customer portal'
          );
        }
      } else {
        toast.error(
          'Unable to identify subscription provider - only Paddle subscriptions are supported'
        );
      }
    } catch {
      toast.error('Failed to access customer portal');
    } finally {
      setPortalLoading(false);
    }
  };

  // Function to handle plan upgrade
  const handlePlanUpgrade = async (
    priceId: string | undefined | null
  ) => {
    if (!priceId) {
      return;
    }
    try {
      setIsLoading(true);
      await handleUpgrade(priceId);
    } catch {
      setIsLoading(false);
    }
  };

  // Sort plans by price for display
  const sortedPlans = [...plans].sort((a, b) => {
    const priceA = parseInt(a.price) || 0;
    const priceB = parseInt(b.price) || 0;
    return priceA - priceB;
  });

  // Get provider display name
  const getProviderDisplay = (provider?: string) => {
    switch (provider) {
      case 'paddle':
        return 'Paddle';
      default:
        return 'Payment Provider';
    }
  };

  return (
    <div className="min-h-screen flex-col bg-background dark:bg-[#0B1739] w-full flex">
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex flex-col items-start justify-between space-y-2 md:flex-row md:items-center md:space-y-0">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">
              Subscription
            </h2>
            <p className="text-muted-foreground">
              Manage your subscription and billing
            </p>
          </div>
        </div>

        {/* Usage Limit Exceeded Alert */}
        {limitExceededInfo && (
          <Alert className="border-destructive bg-destructive/10">
            <AlertTriangle className="h-4 w-4 text-destructive" />
            <AlertDescription className="text-destructive">
              <strong>Usage Limit Reached:</strong>{' '}
              {limitExceededInfo.message}
            </AlertDescription>
          </Alert>
        )}

        <div className="grid gap-6 md:grid-cols-2">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>Current Plan</CardTitle>
              <CardDescription>
                {currentPlan
                  ? `You are currently on the ${currentPlan.name} plan`
                  : "You don't have an active subscription"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-5">
              {currentPlan && activeSubscription ? (
                <>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Badge className="bg-blue-600 text-white mr-2 dark:bg-blue-500 dark:text-white">
                        {currentPlan.name}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {formatPrice(currentPlan.price)}/
                        {currentPlan.interval}
                      </span>
                    </div>
                    <div className="flex flex-col items-end gap-1">
                      <Badge
                        variant="outline"
                        className="bg-green-500/10 text-green-600 border-green-500/20 dark:bg-green-500/10 dark:text-green-400 dark:border-green-500/20"
                      >
                        {activeSubscription.statusFormatted ||
                          'Active'}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        via{' '}
                        {getProviderDisplay(
                          activeSubscription.provider
                        )}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Next billing date
                      </span>
                      <span className="font-medium">
                        {nextBillingDate}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Amount
                      </span>
                      <span className="font-medium">
                        {formatPrice(currentPlan.price)}/
                        {currentPlan.interval}
                      </span>
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={handleCustomerPortal}
                      disabled={portalLoading}
                    >
                      <CreditCard className="mr-2 h-4 w-4" />
                      {portalLoading
                        ? 'Loading...'
                        : 'Manage Billing'}
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handlePlanCancel}
                      disabled={cancelLoading}
                    >
                      {cancelLoading ? 'Canceling...' : 'Cancel'}
                    </Button>
                  </div>
                </>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground mb-4">
                    You don&apos;t have an active subscription
                  </p>
                  <Button>Choose a Plan</Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Usage Card */}
          {currentPlan && (
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle>Usage</CardTitle>
                <CardDescription>
                  Your current usage for this billing period
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {usageLoading ? (
                  <div className="space-y-3">
                    <div className="animate-pulse space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-2 bg-gray-200 rounded"></div>
                    </div>
                    <div className="animate-pulse space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-2 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                ) : usageStats ? (
                  <>
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          Documents processed
                        </span>
                        <span className="font-medium">
                          {usageStats.invoiceUsage} /{' '}
                          {usageStats.invoiceLimit}
                        </span>
                      </div>
                      <Progress
                        value={documentsPercentage}
                        className="h-2"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>
                          {Math.round(documentsPercentage)}% used
                        </span>
                        <span>
                          Resets in {usageStats.daysUntilReset} days
                        </span>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground flex items-center gap-2">
                          <MessageSquare className="h-4 w-4" />
                          Chat messages used
                        </span>
                        <span className="font-medium">
                          {usageStats.chatUsage} /{' '}
                          {usageStats.chatLimit}
                        </span>
                      </div>
                      <Progress
                        value={chatPercentage}
                        className="h-2"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>
                          {Math.round(chatPercentage)}% used
                        </span>
                        <span>
                          Resets on{' '}
                          {usageStats.resetDate.toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground text-sm">
                      Unable to load usage statistics
                    </p>
                  </div>
                )}

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleCustomerPortal}
                  disabled={portalLoading}
                >
                  <CreditCard className="mr-2 h-4 w-4" />
                  {portalLoading
                    ? 'Loading...'
                    : 'Manage Payment Details'}
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        <Tabs defaultValue="plans" className="space-y-4">
          <TabsList className="tabs-container bg-white dark:bg-[#0B1739] border border-[#0097B1]/30 dark:border-[#0097B1]/30 rounded-xl">
            <TabsTrigger value="plans">Available Plans</TabsTrigger>
            <TabsTrigger value="billing">Billing History</TabsTrigger>
          </TabsList>

          <TabsContent value="plans" className="space-y-4">
            <div className="grid gap-6 md:grid-cols-3">
              {sortedPlans.length > 0 ? (
                sortedPlans.map((plan) => (
                  <Card
                    key={plan.id}
                    className={`shadow-sm relative ${currentPlan?.id === plan.id ? 'border-primary' : ''}`}
                  >
                    {currentPlan?.id === plan.id && (
                      <div className="absolute top-0 right-0">
                        <div className="bg-primary text-xs text-primary-foreground py-1 px-3 rounded-bl-lg">
                          Current
                        </div>
                      </div>
                    )}
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <CardTitle className="text-white">
                          {plan.productName || plan.name}
                        </CardTitle>
                        {plan.productName?.includes('Pro') && (
                          <Star className="h-4 w-4 text-amber-400 fill-amber-400" />
                        )}
                        {plan.productName?.includes('Enterprise') && (
                          <Shield className="h-4 w-4 text-blue-500" />
                        )}
                      </div>
                      <CardDescription className="text-gray-400">
                        {plan.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="text-3xl font-bold">
                          {formatPrice(plan.price)}
                          <span className="text-sm font-normal text-muted-foreground">
                            /{plan.interval || 'month'}
                          </span>
                        </div>
                        {currentPlan?.id === plan.id ? (
                          <Button className="w-full">
                            Current Plan
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() =>
                              plan.paddlePriceId &&
                              handlePlanUpgrade(plan.paddlePriceId)
                            }
                            disabled={isLoading}
                          >
                            {isLoading
                              ? 'Processing...'
                              : plan.productName?.includes(
                                'Enterprise'
                              )
                                ? 'Contact Sales'
                                : `Start with ${plan.productName || plan.name}`}
                          </Button>
                        )}
                      </div>
                      <Separator />
                      <div className="space-y-2 text-sm">
                        {/* Plan features based on productName */}
                        <div className="flex items-center">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span className="text-muted-foreground">
                            {plan.productName?.includes('Starter')
                              ? '10'
                              : plan.productName?.includes('Business')
                                ? '100'
                                : plan.productName?.includes(
                                  'Enterprise'
                                )
                                  ? '1000'
                                  : '50'}{' '}
                            invoice uploads/{plan.interval || 'month'}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span className="text-muted-foreground">
                            {plan.productName?.includes('Starter')
                              ? '5'
                              : plan.productName?.includes('Business')
                                ? '50'
                                : plan.productName?.includes(
                                  'Enterprise'
                                )
                                  ? '500'
                                  : '25'}{' '}
                            chat messages/{plan.interval || 'month'}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span className="text-muted-foreground">
                            {plan.productName?.includes('Basic')
                              ? 'Basic'
                              : plan.productName?.includes('Pro')
                                ? 'Advanced'
                                : plan.productName?.includes(
                                  'Starter'
                                )
                                  ? 'Limited'
                                  : 'Premium'}{' '}
                            OCR extraction
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span className="text-muted-foreground">
                            {plan.productName?.includes('Basic')
                              ? 'Basic'
                              : plan.productName?.includes('Pro')
                                ? 'Advanced'
                                : plan.productName?.includes(
                                  'Starter'
                                )
                                  ? 'Basic'
                                  : 'Premium'}{' '}
                            analytics dashboard
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span className="text-muted-foreground">
                            {plan.productName?.includes('Enterprise')
                              ? 'Priority'
                              : plan.productName?.includes('Pro')
                                ? 'Priority'
                                : 'Email'}{' '}
                            support
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-8">
                  <p className="text-muted-foreground">
                    No plans available
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="billing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Billing History</CardTitle>
                <CardDescription>
                  View your past transactions and invoices
                </CardDescription>
              </CardHeader>
              <CardContent>
                {billingLoading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                      </div>
                    ))}
                  </div>
                ) : billingHistory.length > 0 ? (
                  <div className="space-y-4">
                    {billingHistory.map((transaction, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="space-y-1">
                          <p className="font-medium">
                            {transaction.description ||
                              'Subscription'}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {transaction.date
                              ? format(
                                new Date(transaction.date),
                                'MMM dd, yyyy'
                              )
                              : 'N/A'}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            {transaction.amount || 'N/A'}
                          </p>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                transaction.status === 'Paid'
                                  ? 'default'
                                  : 'secondary'
                              }
                              className="text-xs"
                            >
                              {transaction.status || 'Unknown'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">
                      No billing history available
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Subscription;
