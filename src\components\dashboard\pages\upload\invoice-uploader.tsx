'use client';

import {
  useState,
  useEffect,
  useCallback,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Badge } from '@/components/ui/badge';
import { extractInvoiceData } from '@/actions/extract-invoice';
import { useInvoiceDatabase } from '@/hooks/use-invoice-db';
import type { InvoiceData } from '@/types/invoice';
import { ProcessingFile } from './types';
import { Button } from '@/components/ui/button';
import {
  RefreshCw,
  FileUp,
  ArrowDown,
  UploadCloud,
  Check,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { getEmailMetadataFromFile } from '@/lib/services/attachment-converter';
import Colors from '@/components/theme/Colors';

// Sub-components
import { UploadTab } from './upload-tab';
import { ProcessingTab } from './processing-tab';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_FILES = 20;

const formSchema = z.object({
  files: z
    .array(
      z
        .instanceof(File)
        .refine((file) => file.size <= MAX_FILE_SIZE, {
          message: `File size should be less than 10MB`,
        })
        .refine(
          (file) =>
            [
              'image/jpeg',
              'image/png',
              'image/webp',
              'application/pdf',
            ].includes(file.type),
          {
            message:
              'File must be an image (JPEG, PNG, WEBP) or a PDF',
          }
        )
    )
    .refine((files) => files.length > 0, {
      message: 'At least one file is required',
    })
    .refine((files) => files.length <= MAX_FILES, {
      message: `You can upload a maximum of ${MAX_FILES} files at once`,
    }),
});

export type InvoiceUploaderRef = {
  handleFilesFromEmail: (files: File[]) => void;
};

export const InvoiceUploader = forwardRef<InvoiceUploaderRef>(
  (props, ref) => {
    const [processingFiles, setProcessingFiles] = useState<
      ProcessingFile[]
    >([]);
    const [currentProcessingIndex, setCurrentProcessingIndex] =
      useState<number>(-1);
    const [isProcessing, setIsProcessing] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isApiKeyError, setIsApiKeyError] = useState(false);
    const [showSuccess, setShowSuccess] = useState(false);

    // Modern UI with better UX
    const [view, setView] = useState<
      'upload' | 'processing' | 'details'
    >('upload');

    // Initialize the database hook
    const {
      isLoading: isSaving,
      error: dbError,
      saveInvoice,
    } = useInvoiceDatabase();

    const form = useForm<z.infer<typeof formSchema>>({
      resolver: zodResolver(formSchema),
      defaultValues: {
        files: [],
      },
    });

    // Expose the file handling method via ref
    useImperativeHandle(ref, () => ({
      handleFilesFromEmail: (files: File[]) => {
        // Make sure to handle invalid files
        try {
          if (!files || files.length === 0) {
            return;
          }

          // IMPORTANT: Skip form validation for email files and process them directly
          // This bypasses any zod validation issues with file types from email
          processEmailFiles(files);

          // Switch to the processing view automatically
          setView('processing');
        } catch {
          setError('Error processing email attachments');
        }
      },
    }));

    // Special function to handle email files directly - bypassing form validation
    const processEmailFiles = async (files: File[]) => {
      try {
        // Create processing state for each file
        const newProcessingFiles: ProcessingFile[] = [];

        for (const file of files) {
          const previewUrl = await createFilePreview(file);

          // Check if file is from email (has email metadata)
          const emailMetadata = await getEmailMetadataFromFile(file);

          newProcessingFiles.push({
            file,
            progress: 0,
            status: 'pending',
            previewUrl,
            source: 'email',
            emailMetadata: emailMetadata || undefined,
          });
        }

        // Add files to processing queue and explicitly start processing
        setProcessingFiles((prevFiles) => {
          const updatedFiles = [...prevFiles, ...newProcessingFiles];
          return updatedFiles;
        });

        // Force processing to start by directly setting state
        // This ensures processing starts even if the useEffect doesn't trigger
        setTimeout(() => {
          if (!isProcessing && currentProcessingIndex === -1) {
            setIsProcessing(true);
            processNextFile();
          }
        }, 500); // Wait 500ms to ensure state updates have completed
      } catch {
        setError('Error preparing email files for processing');
      }
    };

    // Show database errors
    useEffect(() => {
      if (dbError) {
        setError(dbError);
      }
    }, [dbError]);

    // Count various file statuses
    const completedCount = processingFiles.filter(
      (file) => file.status === 'completed'
    ).length;
    const errorCount = processingFiles.filter(
      (file) => file.status === 'error'
    ).length;

    // Get failed files indices for retry
    const getFailedIndices = useCallback(() => {
      return processingFiles
        .map((file, index) => (file.status === 'error' ? index : -1))
        .filter((index) => index !== -1);
    }, [processingFiles]);

    // Declare processFile first
    const processFile = useCallback(
      async (index: number) => {
        const fileToProcess = processingFiles[index];
        const startTime = Date.now();

        // Don't process files that are already completed, errored or duplicates
        if (
          fileToProcess.status === 'completed' ||
          fileToProcess.status === 'error'
        ) {
          return;
        }

        // Update status to processing
        setProcessingFiles((prev) =>
          prev.map((item, idx) =>
            idx === index
              ? { ...item, status: 'processing', progress: 0 }
              : item
          )
        );

        try {
          // Start simulating progress
          const progressInterval = setInterval(() => {
            setProcessingFiles((prev) =>
              prev.map((item, idx) =>
                idx === index && item.progress < 90
                  ? { ...item, progress: item.progress + 10 }
                  : item
              )
            );
          }, 800);

          const formData = new FormData();
          formData.append('file', fileToProcess.file);

          // Check if this is an email attachment and add metadata
          const emailMetadata = await getEmailMetadataFromFile(
            fileToProcess.file
          );
          if (emailMetadata) {
            formData.append(
              'emailMetadata',
              JSON.stringify(emailMetadata)
            );
          }

          const result = await extractInvoiceData(formData);

          clearInterval(progressInterval);

          // Handle both single and batch extraction results
          const hasError =
            'error' in result
              ? result.error
              : 'errors' in result &&
                result.errors &&
                result.errors.length > 0;

          if (hasError) {
            const errorMsg =
              'error' in result
                ? result.error
                : 'errors' in result
                  ? result.errors?.[0]
                  : 'Unknown error';
            setProcessingFiles((prev) =>
              prev.map((item, idx) =>
                idx === index
                  ? {
                      ...item,
                      status: 'error',
                      error: errorMsg,
                      progress: 100,
                    }
                  : item
              )
            );

            // Check if the error is related to the API key
            if (errorMsg && errorMsg.includes('API key')) {
              setIsApiKeyError(true);
            }
          } else if (result.data) {
            try {
              // Extract single invoice data (for single file processing)
              const invoiceData = Array.isArray(result.data)
                ? result.data[0]
                : result.data;

              if (!invoiceData) {
                throw new Error('No valid invoice data extracted');
              }

              // Check if invoice number is missing and generate a unique one if needed
              if (
                !invoiceData.invoiceNumber ||
                invoiceData.invoiceNumber.trim() === ''
              ) {
                // Generate a unique invoice number using timestamp and random digits
                const timestamp = Date.now().toString();
                const randomDigits = Math.floor(Math.random() * 10000)
                  .toString()
                  .padStart(4, '0');
                invoiceData.invoiceNumber = `INV-${timestamp}-${randomDigits}`;
              }

              // If this is an email attachment, add the source information
              if (emailMetadata) {
                invoiceData.source = {
                  type: 'email',
                  from: emailMetadata.from || 'Unknown',
                  subject: emailMetadata.subject || 'No Subject',
                  date:
                    emailMetadata.date || new Date().toISOString(),
                  messageId: emailMetadata.messageId,
                };
              }

              // Calculate processing time (frontend time for user experience)
              const processingTime = Date.now() - startTime;

              // Ensure meta exists and set processing time
              if (!invoiceData.meta) invoiceData.meta = {};
              invoiceData.meta.processingTime = processingTime;

              // Save to database
              const saveResult = await saveInvoice(invoiceData);

              if (saveResult.success) {
                setProcessingFiles((prev) =>
                  prev.map((item, idx) =>
                    idx === index
                      ? {
                          ...item,
                          status: 'completed',
                          data: invoiceData,
                          progress: 100,
                        }
                      : item
                  )
                );
              } else {
                // Handle save errors
                setProcessingFiles((prev) =>
                  prev.map((item, idx) =>
                    idx === index
                      ? {
                          ...item,
                          status: 'error',
                          error:
                            saveResult.error ||
                            'Database error: Unknown error',
                          data: invoiceData,
                          progress: 100,
                        }
                      : item
                  )
                );
              }
            } catch {
              setProcessingFiles((prev) =>
                prev.map((item, idx) =>
                  idx === index
                    ? {
                        ...item,
                        status: 'error',
                        error: 'Database error: Unknown error',
                        data: Array.isArray(result.data)
                          ? result.data[0]
                          : result.data,
                        progress: 100,
                      }
                    : item
                )
              );
            }
          } else {
            setProcessingFiles((prev) =>
              prev.map((item, idx) =>
                idx === index
                  ? {
                      ...item,
                      status: 'error',
                      error:
                        'No data was returned from the extraction process',
                      progress: 100,
                    }
                  : item
              )
            );
          }
        } catch {
          setProcessingFiles((prev) =>
            prev.map((item, idx) =>
              idx === index
                ? {
                    ...item,
                    status: 'error',
                    error: 'An unexpected error occurred',
                    progress: 100,
                  }
                : item
            )
          );
        }
      },
      [processingFiles, saveInvoice]
    );

    // Define processNextFile without useCallback to avoid circular reference
    function processNextFileFunction(
      startIndex = 0,
      retryIndices?: number[]
    ) {
      // If we're processing specific indices (retry mode)
      if (retryIndices && retryIndices.length > 0) {
        const indexToProcess = retryIndices[0];
        const remainingRetries = retryIndices.slice(1);

        processFile(indexToProcess).then(() => {
          // If we have more retries, continue with them
          if (remainingRetries.length > 0) {
            processNextFileFunction(0, remainingRetries);
          } else {
            setIsProcessing(false);
            setCurrentProcessingIndex(-1);
            setShowSuccess(true);
          }
        });
        return;
      }

      // Standard sequential processing
      let nextIndex = startIndex;

      // Find the next file that's not already completed or failed
      while (
        nextIndex < processingFiles.length &&
        (processingFiles[nextIndex].status === 'completed' ||
          processingFiles[nextIndex].status === 'error')
      ) {
        nextIndex++;
      }

      // If we've processed all files
      if (nextIndex >= processingFiles.length) {
        setIsProcessing(false);
        setCurrentProcessingIndex(-1);
        setShowSuccess(true);
        return;
      }

      setCurrentProcessingIndex(nextIndex);

      // Small timeout to ensure the DOM updates before processing starts
      setTimeout(() => {
        processFile(nextIndex).then(() => {
          processNextFileFunction(nextIndex + 1);
        });
      }, 50);
    }

    // Use the function directly
    const processNextFile = processNextFileFunction;                                                                          

    // Remove console.logs from the useEffect that triggers processing
    useEffect(() => {
      if (
        processingFiles.length > 0 &&
        !isProcessing &&
        currentProcessingIndex === -1
      ) {
        setIsProcessing(true);
        processNextFile();
      }
    }, [
      processingFiles,
      isProcessing,
      currentProcessingIndex,
      processNextFile,
    ]);

    // Add effect to handle view changes
    useEffect(() => {
      if (view === 'upload') {
        // Clear form and files only when switching to upload tab
        form.reset();
        setProcessingFiles([]);
      }
    }, [view, form]);

    const createFilePreview = useCallback(
      (file: File): Promise<string> => {
        return new Promise((resolve) => {
          if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
              resolve(e.target?.result as string);
            };
            reader.readAsDataURL(file);
          } else if (file.type === 'application/pdf') {
            // For PDFs, we'll just show a placeholder
            resolve('/digital-document-stack.png');
          }                                       
        });
      },
      []
    );

    const handleFilesChange = useCallback(
      async (files: File[]) => {
        try {
          form.setValue('files', files, { shouldValidate: true });

          // Create processing state for each file
          const newProcessingFiles: ProcessingFile[] = [];

          for (const file of files) {
            const previewUrl = await createFilePreview(file);

            // Check if file is from email (has email metadata)
            const emailMetadata =
              await getEmailMetadataFromFile(file);

            newProcessingFiles.push({
              file,
              progress: 0,
              status: 'pending',
              previewUrl,
              source: emailMetadata ? 'email' : 'upload',
              emailMetadata: emailMetadata || undefined,
            });
          }

          setProcessingFiles((prevFiles) => [
            ...prevFiles,
            ...newProcessingFiles,
          ]);
          setView('processing');
        } catch {
          setError('Error preparing files for upload');
        }
      },
      [form, createFilePreview]
    );

    const resetForm = useCallback(() => {
      form.reset();
      setProcessingFiles([]);
      setCurrentProcessingIndex(-1);
      setIsProcessing(false);
      setError(null);
      setIsApiKeyError(false);
      setShowSuccess(false);
      setView('upload');
    }, [form]);

    const removeFile = useCallback(
      (index: number) => {
        if (isProcessing) return; // Don't allow removing while processing

        setProcessingFiles((prev) =>
          prev.filter((_, idx) => idx !== index)
        );

        // Update form value
        const currentFiles = form.getValues().files;
        const updatedFiles = currentFiles.filter(
          (_, idx) => idx !== index
        );
        form.setValue('files', updatedFiles, {
          shouldValidate: true,
        });
      },
      [form, isProcessing]
    );

    const updateProcessingData = useCallback(
      (index: number, data: InvoiceData) => {
        setProcessingFiles((prev) =>
          prev.map((item, idx) =>
            idx === index ? { ...item, data } : item
          )
        );
      },
      []
    );

    // Retry failed files
    const handleRetryFailed = useCallback(() => {
      const failedIndices = getFailedIndices();
      if (failedIndices.length > 0 && !isProcessing) {
        setIsProcessing(true);
        processNextFile(0, failedIndices);
      }
    }, [getFailedIndices, isProcessing, processNextFile]);

    // Render the appropriate view
    const renderContent = () => {
      switch (view) {
        case 'upload':
          return (
            <UploadTab
              form={form}
              processingFiles={processingFiles}
              isProcessing={isProcessing}
              maxFiles={MAX_FILES}
              onAddFiles={handleFilesChange}
              onRemoveFile={removeFile}
              onClear={resetForm}
              onViewProcessing={() => setView('processing')}
            />
          );
        case 'processing':
          return (
            <ProcessingTab
              processingFiles={processingFiles}
              isProcessing={isProcessing}
              isSaving={isSaving}
              error={error}
              isApiKeyError={isApiKeyError}
              showSuccess={showSuccess}
              onUpdateData={updateProcessingData}
              onGoToUpload={() => {
                setShowSuccess(false);
                form.reset();
                setProcessingFiles([]);
                setView('upload');
              }}
            />
          );
        case 'details':
          return (
            <div className="space-y-4">
              <Button
                variant="ghost"
                onClick={() => setView('processing')}
                className="mb-4"
              >
                &larr; Back to Processing
              </Button>
              {processingFiles[currentProcessingIndex]?.data && (
                <div className="space-y-4">
                  {/* Render detailed invoice data */}
                  <h2 className="text-xl font-bold">
                    Invoice Details
                  </h2>
                  <pre className="bg-muted p-4 rounded overflow-auto">
                    {JSON.stringify(
                      processingFiles[currentProcessingIndex].data,
                      null,
                      2
                    )}
                  </pre>
                </div>
              )}
            </div>
          );
      }
    };

    // Modern UI with better UX
    return (
      <div className="space-y-6">
        {/* Modern navigation/progress bar */}
        <div className="w-full bg-muted/20 p-1 rounded-lg">
          <div className="flex items-center">
            {/* Upload step */}
            <div
              className={`flex-1 flex items-center justify-center p-2 rounded-md cursor-pointer
            ${view === 'upload' ? 'bg-black dark:bg-secondary text-primary-foreground shadow-sm' : 'hover:bg-muted/50'}`}
              onClick={() => setView('upload')}
            >
              <FileUp className="h-4 w-4 mr-2" />
              <span className="text-sm font-medium">Upload</span>
            </div>

            {/* Processing step */}
            <div
              className={`flex-1 flex items-center justify-center p-2 rounded-md cursor-pointer relative
            ${
              view === 'processing'
                ? 'bg-primary text-primary-foreground shadow-sm'
                : processingFiles.length > 0
                  ? 'hover:bg-muted/50'
                  : 'opacity-50 cursor-not-allowed'
            }`}
              onClick={() =>
                processingFiles.length > 0 && setView('processing')
              }
            >
              <ArrowDown className="h-4 w-4 mr-2" />
              <span className="text-sm font-medium">Processing</span>
              {processingFiles.length > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-2 absolute right-2"
                >
                  {completedCount}/{processingFiles.length}
                </Badge>
              )}
            </div>
          </div>

          {/* Progress bar */}
          {processingFiles.length > 0 && (
            <div className="mt-2 h-1 bg-muted rounded overflow-hidden">
              <div
                className="h-full bg-primary transition-all duration-300 ease-in-out"
                style={{
                  width: `${
                    processingFiles.length > 0
                      ? (completedCount / processingFiles.length) *
                        100
                      : 0
                  }%`,
                }}
              />
            </div>
          )}
        </div>

        {/* Main content area */}
        <Card
          className={`border-[1px] border-[${Colors.border}] shadow-sm bg-card`}
        >
          <CardContent className="p-6">
            {renderContent()}

            {/* Additional UI elements for pending state */}
            {processingFiles.length > 0 && view === 'upload' && (
              <div className="mt-6 flex items-center justify-between p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-2">
                  <UploadCloud className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">
                      Files ready for processing
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {processingFiles.length} file(s) waiting to be
                      processed
                    </p>
                  </div>
                </div>
                <Button onClick={() => setView('processing')}>
                  View Processing Queue
                </Button>
              </div>
            )}

            {/* Retry failed button */}
            {errorCount > 0 &&
              !isProcessing &&
              view === 'processing' && (
                <div className="mt-4 flex justify-center">
                  <Button
                    onClick={handleRetryFailed}
                    className="bg-amber-600 hover:bg-amber-700"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Retry Failed ({errorCount})
                  </Button>
                </div>
              )}

            {/* Duplicate detection has been removed */}

            {/* Success message */}
            {completedCount > 0 &&
              completedCount === processingFiles.length &&
              view === 'processing' && (
                <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300 rounded-lg">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800 dark:text-green-300">
                        All files processed successfully
                      </h3>
                      <div className="mt-2 text-sm">
                        <p className="text-green-700 dark:text-green-400">
                          Successfully processed {completedCount}{' '}
                          {completedCount === 1
                            ? 'invoice'
                            : 'invoices'}
                          . Your invoices have been successfully
                          processed.
                        </p>
                      </div>
                      <div className="mt-4">
                        <div className="-mx-2 -my-1.5 flex">
                          <Button
                            className="px-3 py-1.5 h-auto"
                            variant="default"
                            onClick={resetForm}
                          >
                            Upload More
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
          </CardContent>
        </Card>
      </div>
    );
  }
);

InvoiceUploader.displayName = 'InvoiceUploader';
