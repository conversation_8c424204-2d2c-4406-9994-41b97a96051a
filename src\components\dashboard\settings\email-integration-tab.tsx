"use client";

import { useState, useCallback, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import { runManualEmailSync, isGmailConnected, disconnectGmail, updateEmailSyncSettings } from "@/lib/services/gmail-service";
import { convertEmailAttachmentsToFiles } from "@/lib/services/attachment-converter";
import { RefreshCcw, Check, Mail, ExternalLink, Unlink } from "lucide-react";
import { EmailSyncModal } from "./email-sync-modal";
import { ManualFileUpload } from "@/components/dashboard/pages/upload/manual-file-upload";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface EmailIntegrationTabProps {
  userId: string;
  provider: "gmail";
  onEmailAttachmentsReceived?: (files: File[]) => void;
}

export default function EmailIntegrationTab({ 
  userId, 
  provider, 
  onEmailAttachmentsReceived 
}: EmailIntegrationTabProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean;
    email?: string;
  }>({ connected: false });
  const [autoProcess, setAutoProcess] = useState(true);
  const [includeRead, setIncludeRead] = useState(true);
  const [syncFrequency, setSyncFrequency] = useState<"manual" | "hourly" | "daily" | "weekly" | "monthly">("manual");
  const [isSyncing, setIsSyncing] = useState(false);
  const [showSyncModal, setShowSyncModal] = useState(false);
  const [downloadedFiles, setDownloadedFiles] = useState<File[]>([]);
  const [filesBlobs, setFilesBlobs] = useState<string[]>([]);
  const { toast } = useToast();
  
  // Fetch connection status on mount
  const checkConnectionStatus = useCallback(async () => {
    try {
      if (provider === "gmail") {
        const status = await isGmailConnected(userId);
        setConnectionStatus(status);
        if (!status.connected) {
          toast({
            title: "Email Not Connected",
            description: "Please connect your Gmail account to enable email integration.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      toast({
        title: "Connection Error",
        description: (error instanceof Error ? error.message : 'Failed to check Gmail connection.'),
        variant: "destructive",
      });
    }
  }, [userId, provider, toast]);
  
  // Handle connecting the email account
  const handleConnect = useCallback(() => {
    const connectGmail = async () => {
      // Open the Google OAuth popup
      const width = 600;
      const height = 600;
      const left = window.screenX + (window.outerWidth - width) / 2;
      const top = window.screenY + (window.outerHeight - height) / 2;
      
      const oauthWindow = window.open(
        `/api/email/connect?userId=${userId}&provider=gmail`,
        "Connect Gmail",
        `width=${width},height=${height},top=${top},left=${left}`
      );
      
      // Check if window was blocked by popup blocker
      if (!oauthWindow || oauthWindow.closed || typeof oauthWindow.closed === 'undefined') {
        toast({
          title: "Popup Blocked",
          description: "Please allow popups for this site and try again",
          variant: "destructive",
        });
        return;
      }
      
      // Poll to check when the window is closed
      const checkClosed = setInterval(() => {
        if (oauthWindow.closed) {
          clearInterval(checkClosed);
          checkConnectionStatus();
        }
      }, 500);
    };
    
    connectGmail();
  }, [userId, toast, checkConnectionStatus]);
  
  // Handle disconnecting the email account
  const handleDisconnect = useCallback(async () => {
    try {
      setIsLoading(true);
      if (provider === "gmail") {
        const result = await disconnectGmail(userId);
        setConnectionStatus({ connected: false });
        if (result && result.success) {
          toast({
            title: "Email disconnected",
            description: "Your email account has been disconnected successfully",
          });
        } else {
          toast({
            title: "Disconnection Error",
            description: result && result.error ? result.error : "Failed to disconnect email account.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      toast({
        title: "Disconnection Error",
        description: (error instanceof Error ? error.message : 'Failed to disconnect email account.'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [userId, provider, toast]);
  
  // Update settings
  const updateSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      // Convert "manual" to undefined, otherwise pass the frequency as is
      const frequencyToSave = syncFrequency === "manual" 
        ? undefined 
        : syncFrequency as "hourly" | "daily" | "weekly";
      await updateEmailSyncSettings(userId, {
        autoProcess,
        includeRead,
        frequency: frequencyToSave,
        isActive: syncFrequency !== "manual" // Only active if not manual
      });
      toast({
        title: "Settings Updated",
        description: "Your email sync settings have been updated successfully",
      });
    } catch (error) {
      toast({
        title: "Settings Update Error",
        description: (error instanceof Error ? error.message : 'Failed to update email sync settings.'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [userId, autoProcess, includeRead, syncFrequency, toast]);
  
  // Handle manual sync
  const handleSyncNow = useCallback(() => {
    setShowSyncModal(true);
  }, []);
  
  // Process the sync with selected date range
  const processSync = useCallback(async (dateRange: { startDate: Date; endDate?: Date }) => {
    try {
      setIsSyncing(true);
      const result = await runManualEmailSync(userId, dateRange);
      if (result.success && result.attachments.length > 0) {
        // Convert email attachments to File objects
        const files = await convertEmailAttachmentsToFiles(result.attachments);
        if (files.length > 0) {
          // Store files locally in component state
          setDownloadedFiles(files);
          // Create URL blobs for direct downloads/viewing
          const blobs = files.map(file => URL.createObjectURL(file));
          setFilesBlobs(blobs);
          if (onEmailAttachmentsReceived) {
            // Send the files to be processed by InvoiceUploader
            onEmailAttachmentsReceived(files);
            toast({
              title: "Sync Completed",
              description: `Successfully found ${files.length} invoice attachments. Processing will start automatically.`,
            });
          } else {
            toast({
              title: "Sync Completed",
              description: "Successfully retrieved invoice attachments but cannot send them for processing.",
            });
          }
        } else {
          toast({
            title: "Sync Completed",
            description: "No invoice attachments found in the selected date range",
          });
        }
      } else if (result.error) {
        toast({
          title: "Sync Error",
          description: result.error,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Sync Completed",
          description: "No invoice attachments found in the selected date range",
        });
      }
    } catch (error) {
      toast({
        title: "Sync Failed",
        description: (error instanceof Error ? error.message : 'An error occurred while syncing emails. Please try again.'),
        variant: "destructive",
      });
    } finally {
      setIsSyncing(false);
      setShowSyncModal(false);
    }
  }, [userId, onEmailAttachmentsReceived, toast]);
  
  // Initialize the component - fetch connection status
  useEffect(() => {
    checkConnectionStatus();
  }, [checkConnectionStatus]);
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Email Integration</CardTitle>
              <CardDescription>
                Connect your email account to automatically import invoice attachments
              </CardDescription>
            </div>
            {connectionStatus.connected && (
              <Badge variant="outline" className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 font-normal">
                <Check className="h-3 w-3 mr-1" />
                Connected
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {connectionStatus.connected ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 mr-2 text-muted-foreground" />
                  <span className="text-sm font-medium">{connectionStatus.email}</span>
                </div>
                <Button
                  variant="outline" 
                  size="sm"
                  disabled={isLoading}
                  onClick={handleDisconnect}
                  className="text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-950"
                >
                  <Unlink className="h-4 w-4 mr-1" />
                  Disconnect
                </Button>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Sync Settings</h3>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-process">Auto-process invoices</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically extract data from imported invoices
                    </p>
                  </div>
                  <Switch 
                    id="auto-process" 
                    checked={autoProcess}
                    onCheckedChange={setAutoProcess}
                    disabled={isLoading}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="include-read">Include read emails</Label>
                    <p className="text-sm text-muted-foreground">
                      Include invoices from emails you&apos;ve already read
                    </p>
                  </div>
                  <Switch 
                    id="include-read" 
                    checked={includeRead}
                    onCheckedChange={setIncludeRead}
                    disabled={isLoading}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="sync-frequency">Automatic sync frequency</Label>
                  <Select
                    value={syncFrequency}
                    onValueChange={(value: "manual" | "hourly" | "daily" | "weekly" | "monthly") => setSyncFrequency(value)}
                    disabled={isLoading}
                  >
                    <SelectTrigger id="sync-frequency" className="w-full">
                      <SelectValue placeholder="Select how often to sync" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="manual">Manual only</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    {syncFrequency === 'manual' 
                      ? 'Emails will only be synced when you click "Sync Now"' 
                      : `Emails will be automatically synced ${syncFrequency}`}
                  </p>
                </div>
                
                <Button 
                  className="w-full" 
                  variant="outline"
                  disabled={isLoading}
                  onClick={updateSettings}
                >
                  Save Settings
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-6">
              <Mail className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Connect your email</h3>
              <p className="text-sm text-muted-foreground text-center mb-4">
                Connect your email account to automatically import and process invoice attachments.
              </p>
              <Button 
                variant="outline"
                size="default"
                className="flex items-center gap-2"
                onClick={handleConnect}
              >
                {/* <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="h-5 w-5" fill="none"><rect width="48" height="48" rx="24" fill="#fff"/><path d="M9 17.5V30.5C9 31.3284 9.67157 32 10.5 32H37.5C38.3284 32 39 31.3284 39 30.5V17.5C39 16.6716 38.3284 16 37.5 16H10.5C9.67157 16 9 16.6716 9 17.5Z" fill="#EA4335"/><path d="M39 17.5V30.5C39 31.3284 38.3284 32 37.5 32H10.5C9.67157 32 9 31.3284 9 30.5V17.5" stroke="#EA4335" stroke-width="2"/><path d="M9 17.5L24 27.5L39 17.5" stroke="#34A853" stroke-width="2"/><path d="M24 27.5L9 17.5" stroke="#4285F4" stroke-width="2"/><path d="M24 27.5L39 17.5" stroke="#FBBC05" stroke-width="2"/></svg> */}
                Connect Gmail Account
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={!connectionStatus.connected || isSyncing}
              onClick={handleSyncNow}
              className="flex items-center"
            >
              <RefreshCcw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
              {isSyncing ? 'Syncing...' : 'Sync Now'}
            </Button>
          </div>
          
          <a 
            href="https://mail.google.com/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-sm flex items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            Open Gmail
            <ExternalLink className="h-3 w-3 ml-1" />
          </a>
        </CardFooter>
      </Card>
      
      {/* Direct Manual Upload Section for downloaded files */}
      {downloadedFiles.length > 0 && (
        <div className="space-y-4">
          <Card className="border-green-200 dark:border-green-900 bg-green-50/50 dark:bg-green-950/20">
            <CardHeader>
              <CardTitle>Invoice Email Attachments</CardTitle>
              <CardDescription>
                Files retrieved from your email will be processed automatically.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {downloadedFiles.map((file, index) => (
                  <div key={index} className="border rounded-md p-3 bg-white dark:bg-gray-900 shadow-sm flex flex-col">
                    <div className="font-medium mb-1 truncate">{file.name}</div>
                    <div className="text-xs text-muted-foreground mb-2">
                      {(file.size / 1024).toFixed(1)} KB • {file.type}
                    </div>
                    <div className="mt-auto pt-2 flex gap-2">
                      <a 
                        href={filesBlobs[index]} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        View
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          {/* Manual Processing Component */}
          <ManualFileUpload files={downloadedFiles} autoProcess={true} />
        </div>
      )}
      
      {/* Sync Modal */}
      <EmailSyncModal
        isOpen={showSyncModal}
        onClose={() => setShowSyncModal(false)}
        onSync={processSync}
        isLoading={isSyncing}
      />
    </div>
  );
} 