import { NextResponse } from 'next/server';
import { PerformanceMonitor } from '@/lib/ai/performance-monitor';
import { CacheEngine } from '@/lib/ai/cache-engine';
import { ContextEngine } from '@/lib/ai/context-engine';
import { MemoryEngine } from '@/lib/ai/memory-engine';
import { PredictionEngine } from '@/lib/ai/prediction-engine';
import { AnalyticsEngine } from '@/lib/ai/analytics-engine';
import { getCurrentUserId } from '@/lib/clerk-helpers';

interface SystemStatus {
  overall: 'excellent' | 'good' | 'fair' | 'poor';
  components: {
    performance: {
      status: 'excellent' | 'good' | 'fair' | 'poor';
      avgResponseTime: number;
      successRate: number;
      recommendations: string[];
    };
    cache: {
      status: 'excellent' | 'good' | 'fair' | 'poor';
      hitRate: number;
      size: number;
      recommendations: string[];
    };
    intelligence: {
      status: 'excellent' | 'good' | 'fair' | 'poor';
      contextAccuracy: number;
      predictionConfidence: number;
      recommendations: string[];
    };
  };
  userSpecific?: {
    profileCompleteness: number;
    conversationQuality: number;
    businessInsights: number;
    recommendations: string[];
  };
  systemHealth: {
    uptime: string;
    version: string;
    lastOptimization: Date;
    nextMaintenance: Date;
  };
}

export async function GET() {
  try {
    const userId = await getCurrentUserId();
    
    // Get system-wide performance metrics
    const performanceStats = PerformanceMonitor.getPerformanceStats();
    const cacheStats = CacheEngine.getStats();

    // Performance component status
    const performanceStatus = getPerformanceStatus(performanceStats);
    
    // Cache component status
    const cacheStatus = getCacheStatus(cacheStats);

    // Intelligence component status
    const intelligenceStatus = await getIntelligenceStatus(userId || undefined);

    // User-specific metrics (if authenticated)
    let userSpecific: SystemStatus['userSpecific'] | undefined;
    if (userId) {
      userSpecific = await getUserSpecificStatus(userId);
    }

    // Calculate overall system status
    const componentStatuses = [
      performanceStatus.status,
      cacheStatus.status,
      intelligenceStatus.status
    ];
    const overallStatus = calculateOverallStatus(componentStatuses);

    const systemStatus: SystemStatus = {
      overall: overallStatus,
      components: {
        performance: performanceStatus,
        cache: cacheStatus,
        intelligence: intelligenceStatus
      },
      userSpecific,
      systemHealth: {
        uptime: getUptime(),
        version: '2.0.0-smart',
        lastOptimization: new Date(),
        nextMaintenance: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Next week
      }
    };

    return NextResponse.json(systemStatus);
  } catch (error) {
    console.error('System status error:', error);
    return NextResponse.json(
      { error: 'Failed to get system status' },
      { status: 500 }
    );
  }
}

function getPerformanceStatus(stats: {
  avgResponseTime: number;
  successRate: number;
}): SystemStatus['components']['performance'] {
  let status: 'excellent' | 'good' | 'fair' | 'poor';
  const recommendations: string[] = [];

  if (stats.avgResponseTime < 2000 && stats.successRate > 0.95) {
    status = 'excellent';
  } else if (stats.avgResponseTime < 3000 && stats.successRate > 0.9) {
    status = 'good';
  } else if (stats.avgResponseTime < 5000 && stats.successRate > 0.8) {
    status = 'fair';
    recommendations.push('Consider optimizing response times');
  } else {
    status = 'poor';
    recommendations.push('Immediate performance optimization needed');
  }

  if (stats.avgResponseTime > 4000) {
    recommendations.push('Switch to faster models for simple queries');
  }

  if (stats.successRate < 0.9) {
    recommendations.push('Investigate and fix recurring errors');
  }

  return {
    status,
    avgResponseTime: stats.avgResponseTime,
    successRate: stats.successRate,
    recommendations
  };
}

function getCacheStatus(stats: {
  redisAvailable: boolean;
  memorySize: number;
}): SystemStatus['components']['cache'] {
  let status: 'excellent' | 'good' | 'fair' | 'poor';
  const recommendations: string[] = [];

  // Estimate hit rate based on memory size and Redis availability
  const estimatedHitRate = stats.redisAvailable ? 0.8 : 0.6;

  if (estimatedHitRate > 0.8 && stats.memorySize > 50) {
    status = 'excellent';
  } else if (estimatedHitRate > 0.6 && stats.memorySize > 20) {
    status = 'good';
  } else if (estimatedHitRate > 0.4) {
    status = 'fair';
    recommendations.push('Consider increasing cache TTL');
  } else {
    status = 'poor';
    recommendations.push('Cache optimization needed');
  }

  if (!stats.redisAvailable) {
    recommendations.push('Consider setting up Redis for better caching');
  }

  if (stats.memorySize < 10) {
    recommendations.push('Cache warming may improve performance');
  }

  return {
    status,
    hitRate: estimatedHitRate,
    size: stats.memorySize,
    recommendations
  };
}

async function getIntelligenceStatus(userId?: string): Promise<SystemStatus['components']['intelligence']> {
  let status: 'excellent' | 'good' | 'fair' | 'poor';
  const recommendations: string[] = [];

  try {
    // Test intelligence components
    let contextAccuracy = 0.8; // Default
    let predictionConfidence = 0.7; // Default

    if (userId) {
      // Test actual user context
      const userContext = await ContextEngine.getUserContext(userId);
      contextAccuracy = userContext.business.totalInvoices > 0 ? 0.9 : 0.6;

      // Test predictions
      const insights = await PredictionEngine.generateInsights(userId);
      predictionConfidence = insights.length > 0 ? insights[0].confidence : 0.5;
    }

    if (contextAccuracy > 0.8 && predictionConfidence > 0.7) {
      status = 'excellent';
    } else if (contextAccuracy > 0.6 && predictionConfidence > 0.5) {
      status = 'good';
    } else if (contextAccuracy > 0.4 && predictionConfidence > 0.3) {
      status = 'fair';
      recommendations.push('More user data needed for better insights');
    } else {
      status = 'poor';
      recommendations.push('Intelligence system needs optimization');
    }

    if (contextAccuracy < 0.7) {
      recommendations.push('Improve context data collection');
    }

    if (predictionConfidence < 0.6) {
      recommendations.push('Enhance prediction algorithms');
    }

    return {
      status,
      contextAccuracy,
      predictionConfidence,
      recommendations
    };
  } catch {
    return {
      status: 'poor',
      contextAccuracy: 0.3,
      predictionConfidence: 0.3,
      recommendations: ['Intelligence system error - needs investigation']
    };
  }
}

async function getUserSpecificStatus(userId: string): Promise<SystemStatus['userSpecific']> {
  try {
    const [userProfile, userContext, analytics] = await Promise.all([
      MemoryEngine.getUserProfile(userId),
      ContextEngine.getUserContext(userId),
      AnalyticsEngine.generateAdvancedAnalytics(userId)
    ]);

    // Calculate profile completeness
    const profileFields = [
      userProfile.name !== 'User',
      userProfile.businessType !== 'general',
      userProfile.commonTasks.length > 0,
      userProfile.industryFocus.length > 0,
      userProfile.goals.length > 0
    ];
    const profileCompleteness = profileFields.filter(Boolean).length / profileFields.length;

    // Calculate conversation quality based on context richness
    const conversationQuality = Math.min(1, 
      (userContext.business.totalInvoices / 10) * 0.5 + 
      (userContext.business.recentInvoices.length / 5) * 0.3 +
      (userContext.business.topVendors.length / 3) * 0.2
    );

    // Calculate business insights quality
    const businessInsights = analytics.financialHealth.score / 100;

    const recommendations: string[] = [];
    
    if (profileCompleteness < 0.7) {
      recommendations.push('Complete your profile for better personalization');
    }
    
    if (conversationQuality < 0.5) {
      recommendations.push('Add more business data for richer insights');
    }
    
    if (businessInsights < 0.6) {
      recommendations.push('More transaction history needed for accurate analytics');
    }

    return {
      profileCompleteness,
      conversationQuality,
      businessInsights,
      recommendations
    };
  } catch {
    return {
      profileCompleteness: 0.3,
      conversationQuality: 0.3,
      businessInsights: 0.3,
      recommendations: ['Error analyzing user data']
    };
  }
}

function calculateOverallStatus(componentStatuses: Array<'excellent' | 'good' | 'fair' | 'poor'>): 'excellent' | 'good' | 'fair' | 'poor' {
  const scores = componentStatuses.map(status => {
    switch (status) {
      case 'excellent': return 4;
      case 'good': return 3;
      case 'fair': return 2;
      case 'poor': return 1;
      default: return 1;
    }
  });

  const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

  if (avgScore >= 3.5) return 'excellent';
  if (avgScore >= 2.5) return 'good';
  if (avgScore >= 1.5) return 'fair';
  return 'poor';
}

function getUptime(): string {
  // Simple uptime calculation (in production, this would be more sophisticated)
  const uptimeMs = process.uptime() * 1000;
  const days = Math.floor(uptimeMs / (24 * 60 * 60 * 1000));
  const hours = Math.floor((uptimeMs % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
  const minutes = Math.floor((uptimeMs % (60 * 60 * 1000)) / (60 * 1000));
  
  return `${days}d ${hours}h ${minutes}m`;
}
