import {
  type Message,
  createDataStreamResponse,
  streamText,
} from 'ai';
import { myProvider, selectOptimalModel } from '@/lib/ai/models';
import { ContextEngine, generateContextualPrompt } from '@/lib/ai/context-engine';
import { ActionEngine, detectIntent } from '@/lib/ai/action-engine';
import { MemoryEngine } from '@/lib/ai/memory-engine';
import { PredictionEngine } from '@/lib/ai/prediction-engine';
import { AnalyticsEngine } from '@/lib/ai/analytics-engine';
import { PerformanceMonitor } from '@/lib/ai/performance-monitor';
import { DocumentGenerationEngine } from '@/lib/ai/document-generation-engine';
import { uploadToVercelBlob } from '@/lib/blob';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import { generateUUID } from '@/lib/utils';
import db from '@/db/db';

export async function POST(request: Request) {
  const {
    id,
    messages,
  }: {
    id: string;
    messages: Array<Message>;
  } = await request.json();

  const userId = await getCurrentUserId();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check usage limits
  const { checkCurrentUserChatUsage, incrementCurrentUserChatUsage } =
    await import('@/lib/services/usage-service');

  const usageCheck = await checkCurrentUserChatUsage();
  if (!usageCheck.allowed) {
    return new Response(usageCheck.message || 'Chat limit exceeded', {
      status: 429,
    });
  }

  const userMessage = messages[messages.length - 1];
  if (!userMessage || userMessage.role !== 'user') {
    return new Response('Invalid message format', { status: 400 });
  }

  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 6000); // 6 second timeout
  const startTime = Date.now();

  try {
    // Extract current page from message context (if provided)
    const currentPage = (userMessage as Message & { metadata?: { currentPage?: string } }).metadata?.currentPage || '/dashboard';

    // SMART OPTIMIZATION: Get comprehensive user intelligence with system context
    const [userContext, conversationMemory, userProfile, predictiveInsights, analytics, optimalModel] = await Promise.all([
      ContextEngine.getUserContext(userId, currentPage),
      MemoryEngine.getConversationMemory(id, userId),
      MemoryEngine.getUserProfile(userId),
      PredictionEngine.generateInsights(userId),
      AnalyticsEngine.generateAdvancedAnalytics(userId),
      Promise.resolve(selectOptimalModel(userMessage.content as string))
    ]);

    // Generate enhanced contextual system prompt with full intelligence
    let systemPrompt = generateContextualPrompt(userContext);

    if (conversationMemory) {
      systemPrompt += `\n\n${MemoryEngine.generateConversationSummary(conversationMemory, userProfile)}`;
    }

    // Add proactive insights if relevant
    if (predictiveInsights.length > 0) {
      const topInsight = predictiveInsights[0];
      systemPrompt += `\n\nPROACTIVE INSIGHT: ${topInsight.title} - ${topInsight.description}`;
    }

    // Add business intelligence context
    systemPrompt += `\n\nBUSINESS HEALTH: ${analytics.financialHealth.status} (${analytics.financialHealth.score}/100)`;
    if (analytics.recommendations.length > 0) {
      systemPrompt += `\nTOP RECOMMENDATION: ${analytics.recommendations[0].title}`;
    }

    // Aggressive message truncation - keep only last 2 messages + system context
    const truncatedMessages = messages.slice(-2);

    // Detect if user wants to perform an action
    const intentDetection = detectIntent(userMessage.content as string);
    
    // Detect document generation requests
    const documentIntent = detectDocumentGenerationIntent(userMessage.content as string);

    return createDataStreamResponse({
      execute: async (dataStream) => {
        // Handle document generation first if detected
        if (documentIntent.detected) {
          try {
            console.log(`📄 [DOCUMENT AI] Generating ${documentIntent.type} as ${documentIntent.format}`);
            
            // Generate the document
            const documents = await DocumentGenerationEngine.generateDocument(userId, {
              type: documentIntent.type,
              format: documentIntent.format,
              data: await getDocumentData(userId, documentIntent.type),
              options: {
                includeCharts: true,
                includeImages: true,
                includeBranding: true,
                quality: 'high',
                language: 'en',
                rtl: false
              }
            });

            // Upload documents to blob storage
            const documentsWithUrls = await Promise.all(
              documents.map(async (doc) => {
                const downloadUrl = await createDownloadUrl(doc.buffer, doc.metadata.title, doc.format);
                return {
                  id: doc.id,
                  type: doc.type,
                  format: doc.format,
                  downloadUrl,
                  metadata: doc.metadata,
                  size: doc.buffer.length
                };
              })
            );

            // Stream document generation result
            dataStream.writeData({
              type: 'document-generated',
              documents: documentsWithUrls.map(doc => ({
                id: doc.id,
                type: doc.type,
                format: doc.format,
                downloadUrl: doc.downloadUrl,
                size: doc.size,
                title: doc.metadata.title || 'Document',
                createdAt: new Date().toISOString()
              })),
              message: `✅ Generated ${documents.length} ${documentIntent.type}(s) successfully!`
            });

            console.log(`✅ [DOCUMENT AI] Generated ${documents.length} documents successfully`);
          } catch (error) {
            console.error('Document generation error:', error);
            dataStream.writeData({
              type: 'document-error',
              error: 'Failed to generate document',
              message: 'I apologize, but I encountered an issue generating your document. Please try again.'
            });
          }
        }

        // If high-confidence action detected, execute it
        if (intentDetection.confidence > 0.7) {
          try {
            const actionResult = await ActionEngine.executeAction(
              intentDetection.intent,
              intentDetection.parameters,
              userId
            );

            if (actionResult.success) {
              // Stream the action result as part of the AI response
              dataStream.writeData({
                type: 'action-result',
                result: {
                  success: actionResult.success,
                  message: actionResult.message,
                  type: actionResult.type,
                  data: actionResult.data,
                  intent: intentDetection.intent
                }
              });

              // Handle special actions that require frontend interaction
              if (actionResult.data?.action) {
                dataStream.writeData({
                  type: 'frontend-action',
                  action: actionResult.data.action,
                  parameters: actionResult.data
                });
              }
            }
          } catch (error) {
            console.error('Action execution error:', error);
          }
        }

        // Generate AI response with optimal model selection
        console.log(`🧠 [SMART AI] Using model: ${optimalModel} for query type`);

        const result = streamText({
          model: myProvider.languageModel(optimalModel),
          system: systemPrompt,
          messages: truncatedMessages,
          maxSteps: 1, // Single step for speed
          experimental_generateMessageId: generateUUID,
          abortSignal: controller.signal,
          experimental_telemetry: {
            isEnabled: true,
            functionId: `billix-smart-${optimalModel}`,
          },
        });

        // Stream the response and handle completion
        result.mergeIntoDataStream(dataStream);

        // Handle completion in background (fire and forget)
        setTimeout(async () => {
          try {
            // Generate a better title for the chat
            const generateChatTitle = (content: string): string => {
              // Remove common prefixes and clean the content
              const cleanContent = content
                .replace(/^(hi|hello|hey|good morning|good afternoon|good evening)\s*/i, '')
                .replace(/^(can you|please|could you|would you)\s*/i, '')
                .replace(/^(help me|i need|i want|show me|tell me|explain)\s*/i, '')
                .trim();
              
              // If content is too short, use a default title
              if (cleanContent.length < 10) {
                return 'New Chat';
              }
              
              // Take first 50 characters and add ellipsis if longer
              const title = cleanContent.length > 50 
                ? cleanContent.substring(0, 50) + '...' 
                : cleanContent;
              
              return title || 'New Chat';
            };

            // Ensure chat exists before creating messages
            await db.chat.upsert({
              where: { id },
              update: {
                title: generateChatTitle(userMessage.content as string)
              },
              create: {
                id,
                title: generateChatTitle(userMessage.content as string),
                userId,
                createdAt: new Date(),
                visibility: 'private'
              }
            });

            // Update conversation memory with new message
            await MemoryEngine.updateConversationMemory(id, userId, userMessage);

            // Save the user message
            await db.message.create({
              data: {
                id: generateUUID(),
                chatId: id,
                role: 'user',
                content: JSON.stringify(userMessage.content),
                createdAt: new Date(),
              }
            });

            // Save the AI response (we'll capture it from the stream)
            // Note: The AI response will be saved by the client-side useChat hook
            // which automatically saves messages to the database

            await incrementCurrentUserChatUsage();

            // Clear context cache to ensure fresh data on next request
            ContextEngine.clearCache(userId);

            // Record performance metrics
            PerformanceMonitor.recordMetrics({
              responseTime: Date.now() - startTime,
              tokenUsage: (userMessage.content as string).length + 500, // Estimate
              cacheHitRate: 0.8, // Estimate based on cache usage
              modelUsed: optimalModel,
              success: true,
              userId,
              queryType: detectIntent(userMessage.content as string).intent
            });
          } catch (error) {
            console.error('Failed to save messages:', error);
          }
        }, 100);
      },
      onError: (error) => {
        console.error('Smart chat error:', error);
        return 'I apologize, but I encountered an issue. Please try again.';
      },
    });
  } catch (error) {
    console.error('Critical smart chat error:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: 'Something went wrong. Please try again!'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } finally {
    clearTimeout(timeout);
  }
}

// Helper functions for document generation
function detectDocumentGenerationIntent(message: string): {
  detected: boolean;
  type: 'invoice' | 'report' | 'contract' | 'analysis' | 'dashboard';
  format: 'pdf' | 'excel' | 'image' | 'all';
} {
  const lower = message.toLowerCase();
  
  // Check for document generation keywords
  const isDocumentRequest = lower.includes('create') || lower.includes('generate') || 
                           lower.includes('make') || lower.includes('build') ||
                           lower.includes('pdf') || lower.includes('excel') || 
                           lower.includes('document') || lower.includes('file');
  
  if (!isDocumentRequest) {
    return { detected: false, type: 'invoice', format: 'pdf' };
  }
  
  // Detect document type
  let type: 'invoice' | 'report' | 'contract' | 'analysis' | 'dashboard' = 'invoice';
  if (lower.includes('invoice') || lower.includes('bill')) type = 'invoice';
  else if (lower.includes('report') || lower.includes('summary')) type = 'report';
  else if (lower.includes('contract') || lower.includes('agreement')) type = 'contract';
  else if (lower.includes('analysis') || lower.includes('analytics')) type = 'analysis';
  else if (lower.includes('dashboard')) type = 'dashboard';
  
  // Detect format
  let format: 'pdf' | 'excel' | 'image' | 'all' = 'pdf';
  if (lower.includes('excel') || lower.includes('spreadsheet') || lower.includes('xlsx')) format = 'excel';
  else if (lower.includes('image') || lower.includes('png') || lower.includes('jpg')) format = 'image';
  else if (lower.includes('all') || lower.includes('every')) format = 'all';
  
  return { detected: true, type, format };
}

async function getDocumentData(userId: string, documentType: string): Promise<{
  invoiceNumber?: string;
  vendorName?: string;
  amount?: number;
  issueDate?: Date;
  dueDate?: Date;
  title?: string;
  status?: string;
  lineItems?: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
  generatedAt?: Date;
  data?: { message: string };
}> {
  try {
    switch (documentType) {
      case 'invoice':
        const invoice = await db.invoice.findFirst({
          where: { userId },
          orderBy: { createdAt: 'desc' }
        });
        
        if (invoice) {
          return {
            invoiceNumber: invoice.invoiceNumber || `INV-${Date.now()}`,
            vendorName: invoice.vendorName || 'Client Name',
            amount: invoice.amount || 0,
            issueDate: invoice.issueDate || new Date(),
            dueDate: invoice.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            title: invoice.title || 'Professional services',
            status: invoice.status,
            lineItems: [
              {
                description: invoice.title || 'Professional services',
                quantity: 1,
                unitPrice: invoice.amount || 0,
                totalPrice: invoice.amount || 0
              }
            ]
          };
        }
        
        // Return sample data if no invoice exists
        return {
          invoiceNumber: `INV-${Date.now()}`,
          vendorName: 'Sample Client',
          amount: 1500,
          issueDate: new Date(),
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          title: 'Professional services',
          lineItems: [
            {
              description: 'Consulting services',
              quantity: 10,
              unitPrice: 100,
              totalPrice: 1000
            },
            {
              description: 'Design work',
              quantity: 5,
              unitPrice: 100,
              totalPrice: 500
            }
          ]
        };
        
      default:
        return {
          title: `${documentType} Document`,
          generatedAt: new Date(),
          data: { message: 'Sample document data' }
        };
    }
  } catch (error) {
    console.error('Error getting document data:', error);
    return {
      title: 'Sample Document',
      generatedAt: new Date(),
      data: { message: 'Sample document data' }
    };
  }
}

async function createDownloadUrl(buffer: Buffer, filename: string, format: string): Promise<string> {
  try {
    // Determine content type and extension
    const contentTypes = {
      pdf: 'application/pdf',
      excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      image: 'image/png'
    };
    
    const extensions = {
      pdf: 'pdf',
      excel: 'xlsx', 
      image: 'png'
    };
    
    const contentType = contentTypes[format as keyof typeof contentTypes] || 'application/octet-stream';
    const extension = extensions[format as keyof typeof extensions] || format;
    
    // Create unique filename
    const timestamp = Date.now();
    const cleanFilename = filename.replace(/[^a-zA-Z0-9-_]/g, '-');
    const blobFilename = `documents/${timestamp}-${cleanFilename}.${extension}`;
    
    // Upload to Vercel Blob storage
    const url = await uploadToVercelBlob(blobFilename, buffer, contentType);
    
    if (!url) {
      throw new Error('Failed to upload to blob storage');
    }
    
    return url;
  } catch (error) {
    console.error('Error creating download URL:', error);
    // Fallback: create a data URL (not recommended for large files)
    const base64 = buffer.toString('base64');
    const contentType = format === 'pdf' ? 'application/pdf' : 
                       format === 'excel' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 
                       'image/png';
    return `data:${contentType};base64,${base64}`;
  }
}


