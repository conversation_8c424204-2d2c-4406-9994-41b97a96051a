"use client";

import React, { useCallback, useState } from "react";
import { FileUp, Upload, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FilePreview } from "@/components/dashboard/pages/upload/file-preview";
import { X } from "lucide-react";

interface ProcessingFile {
  file: File;
  previewUrl?: string;
  status: 'pending' | 'processing' | 'completed' | 'error' | 'duplicate';
}

interface FileUploadZoneProps {
  files: ProcessingFile[];
  isProcessing: boolean;
  maxFiles: number;
  onAddFiles: (files: File[]) => void;
  onRemoveFile: (index: number) => void;
}

export function FileUploadZone({ 
  files, 
  isProcessing,
  maxFiles,
  onAddFiles,
  onRemoveFile
}: FileUploadZoneProps) {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const fileList = e.dataTransfer.files;
    if (!fileList || fileList.length === 0) return;
    
    // Convert FileList to array of File objects
    const filesArray = Array.from(fileList).slice(0, maxFiles);
    onAddFiles(filesArray);
  }, [maxFiles, onAddFiles]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = e.target.files;
    if (!fileList || fileList.length === 0) return;
    
    // Convert FileList to array of File objects
    const filesArray = Array.from(fileList).slice(0, maxFiles);
    onAddFiles(filesArray);
  }, [maxFiles, onAddFiles]);

  return (
    <Card
      className={`border-2 border-dashed rounded-xl cursor-pointer transition-colors duration-300 ${
        isDragOver
          ? "border-blue-200/60 dark:border-blue-700/60 bg-gradient-to-br from-blue-50/20 via-white to-cyan-50/20 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20"
          : files.length > 0
            ? "border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10"
            : "border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-br from-white via-gray-50/20 to-white dark:from-gray-800/50 dark:via-gray-700/10 dark:to-gray-800/50 hover:border-blue-200/40 dark:hover:border-blue-700/40"
      }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
        <CardContent className="flex flex-col items-center justify-center p-8 text-center min-h-[320px]">
        {files.length > 0 ? (
          <div className="w-full">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {files.map((file, index) => (
                <div key={index} className="relative group">
                  <div className="relative border border-blue-200/30 dark:border-blue-700/20 rounded-xl overflow-hidden aspect-square bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
                    <FilePreview
                      src={file.previewUrl || ""}
                      type={file.file.type}
                    />
                    <button
                      type="button"
                      onClick={() => onRemoveFile(index)}
                      className="absolute top-3 right-3 bg-red-500 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer"
                      disabled={isProcessing}
                    >
                      <X className="h-4 w-4" />
                    </button>
                    {/* File status indicator */}
                    <div className={`absolute bottom-3 left-3 px-2 py-1 rounded-full text-xs font-medium ${
                      file.status === 'completed' ? 'bg-green-500 text-white' :
                      file.status === 'processing' ? 'bg-blue-500 text-white' :
                      file.status === 'error' ? 'bg-red-500 text-white' :
                      file.status === 'duplicate' ? 'bg-yellow-500 text-white' :
                      'bg-gray-500 text-white'
                    }`}>
                      {file.status}
                    </div>
                  </div>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 truncate font-medium">
                    {file.file.name}
                  </p>
                </div>
              ))}
              {files.length < maxFiles && (
                <div 
                  className="border-2 border-dashed border-gray-200/50 dark:border-gray-700/50 rounded-xl flex items-center justify-center aspect-square cursor-pointer transition-colors duration-300 hover:border-blue-200/40 dark:hover:border-blue-700/40 hover:bg-gradient-to-br hover:from-blue-50/10 hover:to-cyan-50/10 dark:hover:from-blue-950/10 dark:hover:to-cyan-950/10"
                  onClick={() => document.getElementById("file-upload")?.click()}
                >
                  <div className="text-center p-6">
                    <FileUp className="mx-auto h-10 w-10 text-gray-600 dark:text-gray-400" />
                    <p className="mt-3 text-sm text-gray-600 dark:text-gray-400 font-medium">Add more files</p>
                  </div>
                </div>
              )}
            </div>
            <input
              id="file-upload"
              type="file"
              className="hidden"
              multiple
              accept="image/jpeg,image/png,image/webp,application/pdf"
              onChange={handleFileInputChange}
            />
          </div>
        ) : (
          <div className="py-16">
            <div className="mb-6 flex justify-center">
              <div className={`p-6 rounded-xl ${
                isDragOver 
                  ? 'bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40' 
                  : 'bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600'
              } shadow-sm`}>
                <Upload className={`h-16 w-16 ${
                  isDragOver ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'
                }`} />
                {isDragOver && (
                  <Sparkles className="absolute -top-2 -right-2 h-6 w-6 text-blue-600 dark:text-blue-400 animate-bounce" />
                )}
              </div>
            </div>

            <h3 className={`text-2xl font-bold mb-2 ${
              isDragOver ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-gray-100'
            }`}>
              {isDragOver ? 'Drop your files here!' : 'Drag and drop your invoices here'}
            </h3>
            
            <div className="space-y-2 mb-8">
              <p className="text-base text-gray-600 dark:text-gray-400 font-medium">
                Supports JPG, PNG, WEBP and PDF files up to 10MB
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Upload up to {maxFiles} invoices at once
              </p>
            </div>

            <div className="flex justify-center">
              <Button
                type="button"
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0 shadow-lg px-8 py-4 text-base font-semibold rounded-xl cursor-pointer"
                onClick={() =>
                  document.getElementById("file-upload")?.click()
                }
              >
                <FileUp className="mr-3 h-5 w-5" />
                Select Files
              </Button>
            </div>

            <input
              id="file-upload"
              type="file"
              className="hidden"
              multiple
              accept="image/jpeg,image/png,image/webp,application/pdf"
              onChange={handleFileInputChange}
            />
            
            <div className="mt-6 text-xs text-gray-600 dark:text-gray-400">
              or simply drag files from your computer
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 