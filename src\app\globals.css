@import "tailwindcss";
@import "tw-animate-css";
@import "react-day-picker/style.css";

@custom-variant dark (&:is(.dark *));
@plugin "@tailwindcss/typography";

@theme {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.015 286.067);
  --landing-border: #6B7280; /* gray-500 for landing page */
}

.dark {
  /* Figma dashboard dark theme colors */
  --background: #081028;
  --foreground: #FFFFFF;
  --card: #0B1739;
  --card-foreground: #FFFFFF;
  --popover: rgba(11, 23, 57, 0.95);
  --popover-foreground: #FFFFFF;
  --primary: #00BBFF;
  --primary-foreground: #FFFFFF;
  --secondary: #1B3848;
  --secondary-foreground: #FFFFFF;
  --muted: #1B3848;
  --muted-foreground: #E3E8F3;
  --accent: #4000FF;
  --accent-foreground: #FFFFFF;
  --destructive: #DE3D3C;
  --border: rgba(0, 151, 177, 0.2);
  --input: rgba(245, 250, 255, 0.10);
  --ring: #0097B1;
  --chart-1: #00BBFF;
  --chart-2: #4000FF;
  --chart-3: #01ADE6;
  --chart-4: #6359E9;
  --chart-5: #0C8597;
  --sidebar: rgba(8, 16, 40, 0.95);
  --sidebar-foreground: #FFFFFF;
  --sidebar-primary: #00BBFF;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #0097B1;
  --sidebar-accent-foreground: #FFFFFF;
  --sidebar-border: #1B3848;
  --sidebar-ring: #0097B1;
  --landing-border: #6B7280; /* gray-500 for landing page */
}

/* Dashboard dark gradient background */
.dashboard-gradient-bg {
  background: #0a1627 !important;
}

.dark .dashboard-gradient-bg {
  background: #0a1627 !important;
}

/* Landing page always dark theme */
.landing-dark-bg {
  background:
    radial-gradient(ellipse 80% 50% at 60% 0%, rgba(0, 178, 255, 0.15) 0%, rgba(8, 16, 40, 0.8) 60%, #081028 100%),
    linear-gradient(180deg, #081028 0%, #071735 100%);
}

/* Dashboard progress bar styles */
.dashboard-progress-bar {
  width: 100%;
  height: 8px;
  background-color: hsl(var(--muted));
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.dashboard-progress-value {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.dashboard-progress-primary {
  background: linear-gradient(90deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
}

.dashboard-progress-secondary {
  background: linear-gradient(90deg, hsl(var(--chart-2)) 0%, hsl(var(--chart-2) / 0.8) 100%);
}

.dashboard-progress-tertiary {
  background: linear-gradient(90deg, hsl(var(--chart-3)) 0%, hsl(var(--chart-3) / 0.8) 100%);
}

/* Dashboard chart styles */
.dashboard-chart-grid {
  opacity: 0.3;
}

.dashboard-chart-income {
  filter: drop-shadow(0 2px 4px hsl(var(--primary) / 0.1));
}

.dashboard-chart-expenses {
  filter: drop-shadow(0 2px 4px hsl(var(--chart-2) / 0.1));
}

/* Chart responsiveness improvements */
.recharts-responsive-container {
  min-height: 250px;
}

/* Fix for report metric charts - override min-height */
.report-metric-chart .recharts-responsive-container {
  min-height: unset !important;
}

/* Recent invoices scroll styling */
.recent-invoices-scroll::-webkit-scrollbar {
  width: 6px;
}

.recent-invoices-scroll::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 3px;
}

.recent-invoices-scroll::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.recent-invoices-scroll::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Chart tooltip improvements */
.recharts-tooltip-wrapper {
  z-index: 50;
}

/* Chart legend improvements */
.recharts-legend-wrapper {
  padding-bottom: 1rem;
}

/* Card styles with enhanced glow effects - matches the Figma design */
.dark .card-glow {
  background: #0B1739;
  border: 1px solid rgba(0, 151, 177, 0.2);
  box-shadow:
    0 8px 24px rgba(0, 30, 60, 0.4),
    0 0 0 1px rgba(0, 178, 255, 0.15),
    0 0 15px rgba(0, 178, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 24px;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
}

/* Add subtle gradient border to all cards */
.dark .card-glow::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(170deg, rgba(0, 178, 255, 0.6) 0%, rgba(64, 0, 255, 0.3) 100%);
  -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

/* Add subtle inner glow to all cards */
.dark .card-glow::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top left, rgba(0, 178, 255, 0.3) 0%, transparent 70%);
  opacity: 0.5;
  pointer-events: none;
  z-index: 0;
}

/* Figma design card style - dark mode only */
.figma-card {
  position: relative;
  transition: all 0.25s ease;
  overflow: hidden;
  z-index: 1;
  border-radius: 24px;
}

/* Light mode styles */
.figma-card {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  color: #111827;
}

/* Light mode text fixes */
.figma-card h1,
.figma-card h2,
.figma-card h3,
.figma-card h4,
.figma-card h5,
.figma-card h6,
.figma-card p {
  color: #111827;
  position: relative;
  z-index: 2;
}

.figma-card .text-muted-foreground {
  color: #6b7280;
}

/* Clean tab styles that work well in both themes */
.tabs-container {
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  padding: 0.25rem;
}

.tab-trigger {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
  color: hsl(var(--muted-foreground));
  background: transparent;
}

.tab-trigger[data-state=active] {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  font-weight: 600;
  box-shadow: 0 1px 3px hsl(var(--primary) / 0.3);
}

.tab-trigger:hover:not([data-state=active]) {
  background: hsl(var(--muted) / 0.8);
  color: hsl(var(--foreground));
}

/* Dark mode overrides - better contrast and visibility */
.dark .tabs-container {
  background: rgba(11, 23, 57, 0.8);
  border: 1px solid rgba(0, 151, 177, 0.3);
  backdrop-filter: blur(8px);
}

.dark .tab-trigger {
  color: #F1F5F9;
  font-weight: 500;
}

.dark .tab-trigger[data-state=active] {
  background: linear-gradient(135deg, #00BBFF 0%, #0097B1 100%);
  color: #FFFFFF;
  font-weight: 600;
  box-shadow: 
    0 2px 8px rgba(0, 187, 255, 0.4),
    0 0 0 1px rgba(0, 178, 255, 0.3);
}

.dark .tab-trigger:hover:not([data-state=active]) {
  background: rgba(27, 56, 72, 0.7);
  color: #FFFFFF;
}

/* Dark mode styles */
.dark .figma-card {
  background: #0B1739;
  border: 1px solid rgba(0, 151, 177, 0.2);
  box-shadow:
    0 8px 24px rgba(0, 30, 60, 0.4),
    0 0 0 1px rgba(0, 178, 255, 0.15),
    0 0 20px rgba(0, 178, 255, 0.15);
  backdrop-filter: blur(12px);
  color: white;
}

/* Fix for text in figma cards - dark mode only */
.dark .figma-card h1,
.dark .figma-card h2,
.dark .figma-card h3,
.dark .figma-card h4,
.dark .figma-card h5,
.dark .figma-card h6,
.dark .figma-card p {
  color: white;
  position: relative;
  z-index: 2;
}

.dark .figma-card .text-muted-foreground {
  color: rgba(255, 255, 255, 0.7);
}

/* Content inside figma cards should be above the glow effects */
.figma-card>* {
  position: relative;
  z-index: 2;
}

/* Enhanced gradient border for Figma cards - dark mode only */
.dark .figma-card::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(135deg, rgba(0, 187, 255, 0.7) 0%, rgba(64, 0, 255, 0.4) 50%, rgba(0, 178, 255, 0.7) 100%);
  -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  z-index: -1;
}

/* Figma card inner glow - dark mode only */
.dark .figma-card::after {
  content: "";
  position: absolute;
  inset: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 178, 255, 0.35) 0%, transparent 80%);
  opacity: 0.6;
  pointer-events: none;
  z-index: 0;
}

/* Figma card hover effect - dark mode only */
.dark .figma-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 30px rgba(0, 30, 60, 0.5),
    0 0 0 1px rgba(0, 178, 255, 0.3),
    0 0 25px rgba(0, 178, 255, 0.25);
}

.figma-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.dark .figma-card:hover::before {
  background: linear-gradient(135deg, rgba(0, 187, 255, 0.8) 0%, rgba(64, 0, 255, 0.5) 50%, rgba(0, 178, 255, 0.8) 100%);
}

.dark .figma-card:hover::after {
  opacity: 0.8;
}

/* Enhanced Figma card with blue accent */
.figma-card-blue {
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.dark .figma-card-blue {
  box-shadow:
    0 12px 32px rgba(0, 30, 60, 0.5),
    0 0 0 1px rgba(59, 130, 246, 0.25),
    0 0 25px rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.dark .figma-card-blue::before {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(29, 78, 216, 0.5) 50%, rgba(59, 130, 246, 0.8) 100%);
}

.figma-card-blue:hover {
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.25);
  transform: translateY(-3px);
}

.dark .figma-card-blue:hover {
  box-shadow:
    0 16px 40px rgba(0, 30, 60, 0.6),
    0 0 0 1px rgba(59, 130, 246, 0.4),
    0 0 35px rgba(59, 130, 246, 0.3);
}

/* Enhanced Figma card with purple accent */
.figma-card-purple {
  box-shadow: 0 4px 20px rgba(147, 51, 234, 0.15);
  border: 1px solid rgba(147, 51, 234, 0.2);
}

.dark .figma-card-purple {
  box-shadow:
    0 12px 32px rgba(30, 0, 60, 0.5),
    0 0 0 1px rgba(147, 51, 234, 0.25),
    0 0 25px rgba(147, 51, 234, 0.2);
  border: 1px solid rgba(147, 51, 234, 0.3);
}

.dark .figma-card-purple::before {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.8) 0%, rgba(126, 34, 206, 0.5) 50%, rgba(168, 85, 247, 0.8) 100%);
}

.figma-card-purple:hover {
  box-shadow: 0 8px 30px rgba(147, 51, 234, 0.25);
  transform: translateY(-3px);
}

.dark .figma-card-purple:hover {
  box-shadow:
    0 16px 40px rgba(30, 0, 60, 0.6),
    0 0 0 1px rgba(147, 51, 234, 0.4),
    0 0 35px rgba(147, 51, 234, 0.3);
}

.dark .card-premium {
  position: relative;
  background: #0B1739;
  border: 1px solid transparent;
  background-clip: padding-box;
  box-shadow: 0 10px 30px rgba(0, 15, 30, 0.4);
  border-radius: 24px;
}

.dark .card-premium::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(135deg, rgba(0, 178, 255, 0.8) 0%, rgba(64, 0, 255, 0.4) 50%, rgba(0, 178, 255, 0.8) 100%);
  -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

/* Card hover effects */
.dark .card-glow:hover {
  box-shadow:
    0 10px 28px rgba(0, 30, 60, 0.45),
    0 0 0 1px rgba(0, 178, 255, 0.3),
    0 0 20px rgba(0, 178, 255, 0.2);
  transform: translateY(-1px);
}

.dark .card-glow:hover::before {
  background: linear-gradient(170deg, rgba(0, 178, 255, 0.7) 0%, rgba(64, 0, 255, 0.4) 100%);
}

.dark .card-glow:hover::after {
  opacity: 0.6;
}

.dark .card-premium:hover::before {
  background: linear-gradient(135deg, rgba(0, 178, 255, 0.9) 0%, rgba(64, 0, 255, 0.5) 50%, rgba(0, 178, 255, 0.9) 100%);
}

/* Card with blur effect for background blur */
.dark .card-blur {
  background: rgba(11, 23, 57, 0.75);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 151, 177, 0.15);
  border-radius: 24px;
}

/* Stat card variant - small card with icon */
.dark .stat-card {
  background: #0B1739;
  border: 1px solid #0097B1;
  border-radius: 24px;
  box-shadow: 0 6px 16px rgba(0, 10, 20, 0.3);
  overflow: hidden;
  position: relative;
}

.dark .stat-card::after {
  content: "";
  position: absolute;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(0, 178, 255, 0.3) 0%, transparent 70%);
  top: -75px;
  right: -75px;
  border-radius: 100%;
  z-index: 0;
}

/* Add glowing orbs in background - matches the Figma design */
.dark .dashboard-orbs {
  position: relative;
  overflow: hidden;
}

.dark .dashboard-orbs::before {
  content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0, 187, 255, 0.3) 0%, transparent 70%);
  filter: blur(60px);
  top: -150px;
  right: 10%;
  border-radius: 100%;
  z-index: 0;
  animation: float 15s infinite alternate ease-in-out;
}

.dark .dashboard-orbs::after {
  content: "";
  position: absolute;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(64, 0, 255, 0.25) 0%, transparent 70%);
  filter: blur(60px);
  bottom: -100px;
  left: 5%;
  border-radius: 100%;
  z-index: 0;
  animation: float 20s infinite alternate-reverse ease-in-out;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(30px);
  }

  100% {
    transform: translateY(0px);
  }
}

/* Card inner glow effect - adds soft blue inner glow */
.dark .card-inner-glow {
  position: relative;
  overflow: hidden;
}

.dark .card-inner-glow::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(0, 178, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  opacity: 0.4;
  pointer-events: none;
  z-index: 0;
}

/* Card animation effect for interactive cards */
@keyframes card-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 178, 255, 0.2);
  }

  70% {
    box-shadow: 0 0 0 15px rgba(0, 178, 255, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(0, 178, 255, 0);
  }
}

.dark .card-animated {
  animation: card-pulse 2s infinite;
}

/* Enhanced tab styles with improved active indicator - now handled above with new styles */

* {
  border-color: var(--border);
  outline-color: var(--ring);
  outline-width: 50%;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 151, 177, 0.3) transparent;
}

body {
  background-color: var(--background);
  color: var(--foreground);
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 151, 177, 0.3);
  border-radius: 10px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 151, 177, 0.5);
}

/* Hide scrollbar but keep functionality */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Animation for spinning elements */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

/* Voice detection waves animation */
.voice-wave {
  display: inline-block;
  width: 4px;
  height: 20px;
  border-radius: 4px;
  margin: 0 2px;
  background-color: var(--primary);
  animation: voice-wave 1.2s ease-in-out infinite;
}

.voice-wave:nth-child(2) {
  animation-delay: 0.2s;
}

.voice-wave:nth-child(3) {
  animation-delay: 0.4s;
}

.voice-wave:nth-child(4) {
  animation-delay: 0.6s;
}

@keyframes voice-wave {

  0%,
  100% {
    height: 8px;
  }

  50% {
    height: 20px;
  }
}

/* Pulse animation for microphone */
@keyframes mic-pulse {

  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(0, 187, 255, 0.4);
  }

  50% {
    box-shadow: 0 0 0 8px rgba(0, 187, 255, 0);
  }
}

.animate-mic-pulse {
  animation: mic-pulse 1.5s infinite;
}

.glass-effect {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.gradient-border {
  position: relative;
  border: 1px solid transparent;
  background-clip: padding-box;
}

.gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(135deg, rgba(0, 187, 255, 0.6) 0%, rgba(64, 0, 255, 0.2) 50%, rgba(0, 187, 255, 0.6) 100%);
  -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

/* Landing page specific border overrides */
/* Component directory: src/components/Land-page/ */
.landing-page .border-gray-700,
.Land-page .border-gray-700,
[class*="Land-page"] .border-gray-700 {
  border-color: #374151 !important; /* tailwind gray-700 */
}

.landing-page .border-neutral-700,
.Land-page .border-neutral-700,
[class*="Land-page"] .border-neutral-700 {
  border-color: #404040 !important; /* tailwind neutral-700 */
}

.landing-page .border-neutral-600,
.Land-page .border-neutral-600,
[class*="Land-page"] .border-neutral-600 {
  border-color: #525252 !important; /* tailwind neutral-600 */
}

.landing-page .border-white\/10,
.Land-page .border-white\/10,
[class*="Land-page"] .border-white\/10 {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.landing-page .border-white\/20,
.Land-page .border-white\/20,
[class*="Land-page"] .border-white\/20 {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Dark mode versions */
.dark .landing-page .border-gray-700,
.dark .Land-page .border-gray-700,
.dark [class*="Land-page"] .border-gray-700 {
  border-color: #374151 !important;
}

.dark .landing-page .border-neutral-700,
.dark .Land-page .border-neutral-700,
.dark [class*="Land-page"] .border-neutral-700 {
  border-color: #404040 !important;
}

.dark .landing-page .border-neutral-600,
.dark .Land-page .border-neutral-600,
.dark [class*="Land-page"] .border-neutral-600 {
  border-color: #525252 !important;
}

.dark .landing-page .border-white\/10,
.dark .Land-page .border-white\/10,
.dark [class*="Land-page"] .border-white\/10 {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.dark .landing-page .border-white\/20,
.dark .Land-page .border-white\/20,
.dark [class*="Land-page"] .border-white\/20 {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* New landing page border class */
.landing-border {
  border-color: var(--landing-border) !important;
}

/* Apply landing page border to all components in the landing page */
.landing-dark-bg * {
  --border: var(--landing-border);
}

.skeleton {
  * {
      pointer-events: none !important;
  }

  *[class^="text-"] {
      color: transparent;
      @apply rounded-md bg-foreground/20 select-none animate-pulse;
  }

  .skeleton-bg {
      @apply bg-foreground/10;
  }

  .skeleton-div {
      @apply bg-foreground/20 animate-pulse;
  }
}

.ProseMirror {
  outline: none;
}

.cm-editor,
.cm-gutters {
  @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
  @apply bg-zinc-200 dark:bg-zinc-900;
}

.cm-activeLine,
.cm-activeLineGutter {
  @apply bg-transparent;
}

.cm-activeLine {
  @apply rounded-r-sm;
}

.cm-lineNumbers {
  @apply min-w-7;
}

.cm-foldGutter {
  @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
  @apply rounded-l-sm;
}

.suggestion-highlight {
  @apply bg-blue-200 hover:bg-blue-300 dark:hover:bg-blue-400/50 dark:text-blue-50 dark:bg-blue-500/40;
}

@theme inline {
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom utilities for glassmorphic design */
@layer utilities {
  .bg-gradient-radial {
    background-image: radial-gradient(circle, var(--tw-gradient-stops));
  }
  
  .bg-noise {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.85' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  }
}