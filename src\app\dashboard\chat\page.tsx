import { cookies } from 'next/headers';

import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { generateUUID } from '@/lib/utils';
import { Chat } from '@/components/ai-agent/chat';
import { DataStreamHandler } from '@/components/ai-agent/data-stream-handler';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { UsageGuard } from '@/components/dashboard/usage/UsageGuard';
import SubscriptionGuard from '@/components/guards/SubscriptionGuard';

export default async function Page() {
  const id = generateUUID();

  const cookieStore = await cookies();
  const modelIdFromCookie = cookieStore.get('chat-model');

  if (!modelIdFromCookie) {
    return (
      <SubscriptionGuard>
        <DashboardLayout>
          <UsageGuard feature="chat">
            <Chat
              key={id}
              id={id}
              initialMessages={[]}
              selectedChatModel={DEFAULT_CHAT_MODEL}
              selectedVisibilityType="private"
              isReadonly={false}
            />
            <DataStreamHandler id={id} />
          </UsageGuard>
        </DashboardLayout>
      </SubscriptionGuard>
    );
  }

  return (
    <SubscriptionGuard>
      <DashboardLayout>
        <UsageGuard feature="chat">
          <Chat
            key={id}
            id={id}
            initialMessages={[]}
            selectedChatModel={modelIdFromCookie.value}
            selectedVisibilityType="private"
            isReadonly={false}
          />
          <DataStreamHandler id={id} />
        </UsageGuard>
      </DashboardLayout>
    </SubscriptionGuard>
  );
}
