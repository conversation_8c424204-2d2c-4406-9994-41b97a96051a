'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { 
  Send, 
  Download, 
  FileText, 
  Sparkles, 
  Zap,
  Brain,
  TrendingUp
} from 'lucide-react';
import { generateUUID } from '@/lib/utils';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata?: {
    model?: string;
    responseTime?: number;
    tokenUsage?: number;
    actionExecuted?: string;
    documentsGenerated?: number;
  };
}

interface UIAction {
  type: 'navigate' | 'modal' | 'toast' | 'update' | 'refresh' | 'download';
  target?: string;
  payload?: any;
  delay?: number;
}

interface GeneratedDocument {
  id: string;
  type: 'pdf' | 'excel' | 'image';
  url: string;
  filename: string;
  size: number;
}

export function UltraSmartChat() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [chatId] = useState(() => generateUUID());
  const [documents, setDocuments] = useState<GeneratedDocument[]>([]);
  const [stats, setStats] = useState({
    totalTokens: 0,
    totalCost: 0,
    avgResponseTime: 0,
    actionsExecuted: 0
  });
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleUIActions = (actions: UIAction[]) => {
    actions.forEach((action, index) => {
      setTimeout(() => {
        switch (action.type) {
          case 'toast':
            toast[action.payload.type || 'info'](action.payload.message);
            break;
          case 'navigate':
            if (action.target) {
              window.location.href = action.target;
            }
            break;
          case 'download':
            if (action.payload.url) {
              const link = document.createElement('a');
              link.href = action.payload.url;
              link.download = action.payload.filename || 'download';
              link.click();
            }
            break;
          case 'refresh':
            if (action.target === 'invoice-list') {
              // Trigger invoice list refresh
              window.dispatchEvent(new CustomEvent('refresh-invoices'));
            }
            break;
        }
      }, (action.delay || 0) + index * 500);
    });
  };

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: generateUUID(),
      role: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/chat/ultra-smart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [...messages, userMessage].map(m => ({
            role: m.role,
            content: m.content
          })),
          chatId,
          context: {
            page: window.location.pathname,
            userIntent: detectUserIntent(input)
          },
          options: {
            generateDocuments: true,
            includeInsights: true,
            smartMode: true,
            learningEnabled: true
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      let assistantMessage: Message = {
        id: generateUUID(),
        role: 'assistant',
        content: '',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'text') {
                assistantMessage.content += data.content;
                setMessages(prev => prev.map(m => 
                  m.id === assistantMessage.id ? { ...assistantMessage } : m
                ));
              }
              
              if (data.type === 'action_result') {
                if (data.data.uiActions) {
                  handleUIActions(data.data.uiActions);
                }
                
                setStats(prev => ({
                  ...prev,
                  actionsExecuted: prev.actionsExecuted + 1
                }));
              }
              
              if (data.type === 'documents_generated') {
                setDocuments(prev => [...prev, ...data.data.documents]);
                toast.success(`${data.data.documents.length} document(s) generated!`);
              }
              
              if (data.type === 'completion') {
                assistantMessage.metadata = {
                  model: data.data.model,
                  responseTime: data.data.responseTime,
                  tokenUsage: data.data.tokenUsage,
                  actionExecuted: data.data.actionExecuted,
                  documentsGenerated: data.data.documentsGenerated
                };
                
                setStats(prev => ({
                  totalTokens: prev.totalTokens + (data.data.tokenUsage || 0),
                  totalCost: prev.totalCost + (data.data.estimatedCost || 0),
                  avgResponseTime: (prev.avgResponseTime + data.data.responseTime) / 2,
                  actionsExecuted: prev.actionsExecuted
                }));
                
                setMessages(prev => prev.map(m => 
                  m.id === assistantMessage.id ? { ...assistantMessage } : m
                ));
              }
              
            } catch (error) {
              console.error('Failed to parse streaming data:', error);
            }
          }
        }
      }

    } catch (error) {
      console.error('Chat error:', error);
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const detectUserIntent = (message: string): string => {
    const lowerMessage = message.toLowerCase();
    if (lowerMessage.includes('create') || lowerMessage.includes('generate')) return 'create';
    if (lowerMessage.includes('analyze') || lowerMessage.includes('report')) return 'analyze';
    if (lowerMessage.includes('list') || lowerMessage.includes('show')) return 'list';
    return 'general';
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Header with Stats */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-blue-500" />
            Ultra-Smart AI Agent
            <Badge variant="secondary" className="ml-auto">
              <Sparkles className="h-3 w-3 mr-1" />
              Advanced Mode
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-blue-600">{stats.totalTokens.toLocaleString()}</div>
              <div className="text-muted-foreground">Tokens Used</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-600">${stats.totalCost.toFixed(4)}</div>
              <div className="text-muted-foreground">Total Cost</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-purple-600">{Math.round(stats.avgResponseTime)}ms</div>
              <div className="text-muted-foreground">Avg Response</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-orange-600">{stats.actionsExecuted}</div>
              <div className="text-muted-foreground">Actions Executed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto space-y-4 mb-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <Card className={`max-w-[80%] ${
              message.role === 'user' 
                ? 'bg-blue-500 text-white' 
                : 'bg-muted'
            }`}>
              <CardContent className="p-4">
                <div className="whitespace-pre-wrap">{message.content}</div>
                {message.metadata && (
                  <div className="mt-2 text-xs opacity-70 flex items-center gap-2">
                    <Zap className="h-3 w-3" />
                    {message.metadata.model} • {message.metadata.responseTime}ms • {message.metadata.tokenUsage} tokens
                    {message.metadata.actionExecuted && (
                      <>
                        • <TrendingUp className="h-3 w-3" />
                        {message.metadata.actionExecuted}
                      </>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <Card className="bg-muted">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                  <span>AI is thinking...</span>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Generated Documents */}
      {documents.length > 0 && (
        <Card className="mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Generated Documents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {documents.map((doc) => (
                <Button
                  key={doc.id}
                  variant="outline"
                  className="justify-start"
                  onClick={() => window.open(doc.url, '_blank')}
                >
                  <Download className="h-4 w-4 mr-2" />
                  {doc.filename}
                  <Badge variant="secondary" className="ml-auto">
                    {doc.type.toUpperCase()}
                  </Badge>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Input */}
      <div className="flex gap-2">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Ask me anything... I can create invoices, generate reports, analyze data, and more!"
          disabled={isLoading}
          className="flex-1"
        />
        <Button 
          onClick={sendMessage} 
          disabled={isLoading || !input.trim()}
          size="icon"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
