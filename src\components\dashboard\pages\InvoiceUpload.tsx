'use client';

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent } from '@/components/ui/card';
import { getUserIdFromClerkId } from '@/lib/actions/user';
import { useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { FileUp, Inbox, Sparkles } from 'lucide-react';
import EmailIntegrationTab from '@/components/dashboard/settings/email-integration-tab';
import AIFeaturesCard from '@/components/dashboard/invoice/AIFeaturesCard';
import { InvoiceUploader } from '@/components/dashboard/pages/upload/invoice-uploader';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { useRef } from 'react';
import { toast } from 'sonner';

const InvoiceUpload = () => {
  const [dbUserId, setDbUserId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('file');
  const [pendingEmailFiles, setPendingEmailFiles] = useState<File[]>(
    []
  );
  const { userId: clerkUserId } = useAuth();
  const router = useRouter();

  // Create ref to store invoice uploader instance for communication between tabs
  const invoiceUploaderRef = useRef<{
    handleFilesFromEmail: (files: File[]) => void;
  } | null>(null);

  useEffect(() => {
    const fetchUserId = async () => {
      if (!clerkUserId) return;

      try {
        // Call server action instead of db directly
        const userId = await getUserIdFromClerkId(clerkUserId);
        if (userId) {
          setDbUserId(userId);
        }
      } catch {
        toast.error("Failed to fetch user details");
      }
    };

    fetchUserId();
  }, [clerkUserId]);

  // Process any pending email files when the ref becomes available
  useEffect(() => {
    // Check if there are pending files and the ref is available
    if (pendingEmailFiles.length > 0 && invoiceUploaderRef.current) {
      // Use a small timeout to ensure the tab switch completes
      setTimeout(() => {
        if (invoiceUploaderRef.current) {
          invoiceUploaderRef.current.handleFilesFromEmail([
            ...pendingEmailFiles,
          ]);
          // Clear pending files after processing
          setPendingEmailFiles([]);
        } else {
          // This case shouldn't happen since we're processing immediately
          toast.info('Files ready for processing - Switch to the File Upload tab to process your attachments');
        }
      }, 100);
    }
  }, [activeTab, pendingEmailFiles]); // Check when tabs change or component remounts

  const navigateToAISettings = () => {
    router.push('/dashboard/settings/ai');
  };

  // Handle email attachments being received
  const handleEmailAttachments = (files: File[]) => {
    // Try to forward the files to the InvoiceUploader component
    if (invoiceUploaderRef.current) {
      // Switch to file tab first to ensure the component is mounted
      setActiveTab('file');

      // Use a small timeout to ensure the tab switch completes
      setTimeout(() => {
        if (invoiceUploaderRef.current) {
          invoiceUploaderRef.current.handleFilesFromEmail(files);
        } else {
          // Store the files for later processing
          setPendingEmailFiles((prev) => [...prev, ...files]);

          toast.info('Files ready for processing - Switch to the File Upload tab to process your attachments');
        }
      }, 100);
    } else {
      // Store the files for later processing
      setPendingEmailFiles((prev) => [...prev, ...files]);

      toast.info('Files ready for processing - Switch to the File Upload tab to process your attachments');
    }
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-background via-blue-50/20 to-cyan-50/20 dark:from-[#0B1739] dark:via-blue-950/10 dark:to-cyan-950/10 w-full flex flex-col space-y-8 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 dark:from-white dark:via-blue-200 dark:to-indigo-200 bg-clip-text text-transparent">
              Invoice Management
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 font-medium">
              Upload, process and manage your invoices with AI assistance
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard/invoices')}
              className="border border-gray-200/50 dark:border-gray-700/50 text-gray-700 dark:text-gray-300 rounded-xl font-medium cursor-pointer"
            >
              <Inbox className="mr-2 h-4 w-4" />
              View All Invoices
            </Button>
            <Button
              onClick={navigateToAISettings}
              className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0 shadow-lg rounded-xl font-medium cursor-pointer"
            >
              <Sparkles className="mr-2 h-4 w-4" />
              AI Settings
            </Button>
          </div>
        </div>

        <Separator />

        {/* Main content area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-8">
            <Card className="border border-blue-200/30 dark:border-blue-700/20 overflow-hidden rounded-xl bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="w-full p-1 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-xl border-b border-blue-200/30 dark:border-blue-700/20">
                  <TabsTrigger
                    value="file"
                    className="flex-1 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white font-medium cursor-pointer"
                  >
                    <FileUp className="h-5 w-5 mr-2" />
                    File Upload
                  </TabsTrigger>
                  <TabsTrigger
                    value="email"
                    className="flex-1 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white font-medium cursor-pointer"
                  >
                    <Inbox className="h-5 w-5 mr-2" />
                    Email Integration
                  </TabsTrigger>
                </TabsList>

                <CardContent className="p-0">
                  <TabsContent value="file" className="mt-0 p-8 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
                    <InvoiceUploader ref={invoiceUploaderRef} />
                  </TabsContent>

                  <TabsContent value="email" className="mt-0 p-8 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
                    {dbUserId ? (
                      <EmailIntegrationTab
                        userId={dbUserId}
                        provider="gmail"
                        onEmailAttachmentsReceived={
                          handleEmailAttachments
                        }
                      />
                    ) : (
                      <div className="p-8 text-center">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
                        <p className="text-lg font-medium text-gray-600 dark:text-gray-400">Fetching user details... Please wait.</p>
                      </div>
                    )}
                  </TabsContent>
                </CardContent>
              </Tabs>
            </Card>
          </div>

          <div>
            <AIFeaturesCard onConfigureAI={navigateToAISettings} />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default InvoiceUpload;
