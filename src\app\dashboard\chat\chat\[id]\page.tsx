import { Chat } from '@/components/ai-agent/chat';
import { DataStreamHandler } from '@/components/ai-agent/data-stream-handler';
import { VisibilityType } from '@/hooks/use-chat-visibility';
import { UsageGuard } from '@/components/dashboard/usage/UsageGuard';
import db from '@/db/db';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import { convertToUIMessages } from '@/lib/utils';
import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';

export default async function Page(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  const { id } = params;

  // Get chat from database
  const chat = await db.chat.findUnique({
    where: { id },
  });

  if (!chat) {
    notFound();
  }

  // Get current user ID
  const userId = await getCurrentUserId();

  // Check visibility permissions
  if (chat.visibility === 'private') {
    if (!userId) {
      return notFound();
    }

    if (userId !== chat.userId) {
      return notFound();
    }
  }

  // Get messages for this chat
  const messagesFromDb = await db.message.findMany({
    where: { chatId: id },
    orderBy: { createdAt: 'asc' },
  });

  const cookieStore = await cookies();
  const chatModelFromCookie = cookieStore.get('chat-model');

  if (!chatModelFromCookie) {
    return (
      <UsageGuard feature="chat">
        <Chat
          id={chat.id}
          initialMessages={convertToUIMessages(messagesFromDb)}
          selectedChatModel={DEFAULT_CHAT_MODEL}
          selectedVisibilityType={chat.visibility as VisibilityType}
          isReadonly={userId !== chat.userId}
        />
        <DataStreamHandler id={id} />
      </UsageGuard>
    );
  }

  return (
    <UsageGuard feature="chat">
      <Chat
        id={chat.id}
        initialMessages={convertToUIMessages(messagesFromDb)}
        selectedChatModel={chatModelFromCookie.value}
        selectedVisibilityType={chat.visibility as VisibilityType}
        isReadonly={userId !== chat.userId}
      />
      <DataStreamHandler id={id} />
    </UsageGuard>
  );
}
