import { NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import db from '@/db/db';

export async function GET() {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find the database user first
    const dbUser = await db.user.findUnique({
      where: { clerkId: user.id },
    });

    if (!dbUser) {
      return NextResponse.json({
        status: 'inactive',
        plan: {
          name: 'No Subscription',
          price: '0.00',
          currency: 'USD',
          billingInterval: 'none',
        },
        startDate: null,
        endDate: null,
        renewalDate: null,
        isActive: false,
      });
    }

    // Get the most recent active subscription for this user
    const subscription = await db.subscription.findFirst({
      where: {
        userId: dbUser.id,
        status: {
          in: ['active', 'trialing', 'past_due'],
        },
      },
      orderBy: {
        id: 'desc',
      },
      include: {
        plan: true,
      },
    });

    if (!subscription) {
      return NextResponse.json({
        status: 'inactive',
        plan: {
          name: 'No Subscription',
          price: '0.00',
          currency: 'USD',
          billingInterval: 'none',
        },
        startDate: null,
        endDate: null,
        renewalDate: null,
        isActive: false,
      });
    }

    // Format the subscription data for the client
    const formattedSubscription = {
      id: subscription.id.toString(),
      status: subscription.status,
      plan: {
        name: subscription.plan.name,
        price: subscription.price,
        currency: 'USD',
        billingInterval: subscription.plan.interval || 'month',
      },
      startDate: new Date().toISOString(), // Fallback
      endDate: subscription.endsAt,
      renewalDate: subscription.renewsAt,
      isActive:
        subscription.status !== 'cancelled' &&
        subscription.status !== 'expired',
    };

    return NextResponse.json(formattedSubscription);
  } catch (error) {
    console.error('Error fetching active subscription:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription data' },
      { status: 500 }
    );
  }
}
