import { DocumentGenerationEngine } from './document-generation-engine';
import { CacheEngine } from './cache-engine';
import { uploadToVercelBlob } from '@/lib/blob';
import { generateUUID } from '@/lib/utils';

interface StreamingDocumentRequest {
  type: 'invoice' | 'report' | 'analysis' | 'spreadsheet';
  format: 'pdf' | 'excel' | 'image' | 'all';
  data: any;
  userId: string;
  onProgress?: (progress: StreamingProgress) => void;
  onComplete?: (documents: StreamingDocument[]) => void;
  onError?: (error: string) => void;
}

interface StreamingProgress {
  stage: 'preparing' | 'generating' | 'formatting' | 'uploading' | 'complete';
  progress: number; // 0-100
  message: string;
  currentStep?: string;
  estimatedTimeRemaining?: number;
}

interface StreamingDocument {
  id: string;
  type: 'pdf' | 'excel' | 'image';
  url: string;
  filename: string;
  size: number;
  previewUrl?: string;
  metadata: {
    title: string;
    pages?: number;
    sheets?: number;
    createdAt: string;
    processingTime: number;
  };
}

/**
 * Streaming Document Generation Engine
 * Provides real-time progress updates during document creation
 */
export class StreamingDocumentEngine {
  private static activeGenerations = new Map<string, boolean>();

  /**
   * Generate documents with real-time streaming progress
   */
  static async generateWithStreaming(request: StreamingDocumentRequest): Promise<StreamingDocument[]> {
    const generationId = generateUUID();
    const startTime = Date.now();
    
    this.activeGenerations.set(generationId, true);
    
    try {
      // Stage 1: Preparing
      this.updateProgress(request, {
        stage: 'preparing',
        progress: 10,
        message: 'Preparing document generation...',
        currentStep: 'Analyzing data and selecting templates'
      });

      // Get optimal template and branding
      const template = await DocumentGenerationEngine.getOptimalTemplate(request.type, request.userId);
      const branding = await DocumentGenerationEngine.getUserBranding(request.userId);

      // Stage 2: Generating
      this.updateProgress(request, {
        stage: 'generating',
        progress: 30,
        message: 'Generating document content...',
        currentStep: `Creating ${request.type} with ${request.format} format`
      });

      const documents: StreamingDocument[] = [];

      // Generate based on format
      if (request.format === 'pdf' || request.format === 'all') {
        const pdfDoc = await this.generatePDFWithProgress(request, template, branding);
        documents.push(pdfDoc);
      }

      if (request.format === 'excel' || request.format === 'all') {
        const excelDoc = await this.generateExcelWithProgress(request, template, branding);
        documents.push(excelDoc);
      }

      if (request.format === 'image' || request.format === 'all') {
        const imageDoc = await this.generateImageWithProgress(request, template, branding);
        documents.push(imageDoc);
      }

      // Stage 3: Uploading
      this.updateProgress(request, {
        stage: 'uploading',
        progress: 90,
        message: 'Uploading documents to cloud storage...',
        currentStep: 'Securing and optimizing files'
      });

      // Upload all documents
      for (const doc of documents) {
        if (doc.url.startsWith('blob:')) {
          // Convert blob URL to permanent storage
          const response = await fetch(doc.url);
          const blob = await response.blob();
          const uploadResult = await uploadToVercelBlob(blob, doc.filename);
          doc.url = uploadResult.url;
        }
      }

      // Stage 4: Complete
      const processingTime = Date.now() - startTime;
      this.updateProgress(request, {
        stage: 'complete',
        progress: 100,
        message: `Successfully generated ${documents.length} document(s)`,
        currentStep: 'Ready for download'
      });

      // Update metadata with processing time
      documents.forEach(doc => {
        doc.metadata.processingTime = processingTime;
      });

      // Cache the results
      await CacheEngine.set(
        `generated-docs-${generationId}`,
        documents,
        'action-results'
      );

      // Call completion callback
      if (request.onComplete) {
        request.onComplete(documents);
      }

      return documents;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (request.onError) {
        request.onError(errorMessage);
      }
      
      throw error;
    } finally {
      this.activeGenerations.delete(generationId);
    }
  }

  /**
   * Generate PDF with progress updates
   */
  private static async generatePDFWithProgress(
    request: StreamingDocumentRequest,
    template: any,
    branding: any
  ): Promise<StreamingDocument> {
    this.updateProgress(request, {
      stage: 'generating',
      progress: 40,
      message: 'Creating PDF document...',
      currentStep: 'Applying templates and styling'
    });

    const docRequest = {
      type: request.type,
      format: 'pdf' as const,
      data: request.data,
      template,
      branding,
      options: {
        includeCharts: true,
        includeImages: true,
        includeBranding: true,
        quality: 'high',
        language: 'en',
        rtl: false
      }
    };

    const generatedDoc = await DocumentGenerationEngine.generateDocument(request.userId, docRequest);
    const pdfDoc = generatedDoc[0];

    this.updateProgress(request, {
      stage: 'formatting',
      progress: 60,
      message: 'Formatting PDF layout...',
      currentStep: 'Optimizing for print and digital viewing'
    });

    return {
      id: pdfDoc.id,
      type: 'pdf',
      url: pdfDoc.downloadUrl,
      filename: `${request.type}_${Date.now()}.pdf`,
      size: pdfDoc.metadata?.size || 0,
      metadata: {
        title: pdfDoc.metadata?.title || `${request.type} Document`,
        pages: pdfDoc.metadata?.pages || 1,
        createdAt: new Date().toISOString(),
        processingTime: 0 // Will be updated later
      }
    };
  }

  /**
   * Generate Excel with progress updates
   */
  private static async generateExcelWithProgress(
    request: StreamingDocumentRequest,
    template: any,
    branding: any
  ): Promise<StreamingDocument> {
    this.updateProgress(request, {
      stage: 'generating',
      progress: 50,
      message: 'Creating Excel spreadsheet...',
      currentStep: 'Building worksheets and charts'
    });

    const docRequest = {
      type: request.type,
      format: 'excel' as const,
      data: request.data,
      template,
      branding,
      options: {
        includeCharts: true,
        includeImages: true,
        includeBranding: true,
        quality: 'high',
        language: 'en',
        rtl: false
      }
    };

    const generatedDoc = await DocumentGenerationEngine.generateDocument(request.userId, docRequest);
    const excelDoc = generatedDoc[0];

    this.updateProgress(request, {
      stage: 'formatting',
      progress: 70,
      message: 'Formatting Excel worksheets...',
      currentStep: 'Adding formulas and conditional formatting'
    });

    return {
      id: excelDoc.id,
      type: 'excel',
      url: excelDoc.downloadUrl,
      filename: `${request.type}_${Date.now()}.xlsx`,
      size: excelDoc.metadata?.size || 0,
      metadata: {
        title: excelDoc.metadata?.title || `${request.type} Spreadsheet`,
        sheets: 3, // Estimate based on document type
        createdAt: new Date().toISOString(),
        processingTime: 0
      }
    };
  }

  /**
   * Generate Image with progress updates
   */
  private static async generateImageWithProgress(
    request: StreamingDocumentRequest,
    template: any,
    branding: any
  ): Promise<StreamingDocument> {
    this.updateProgress(request, {
      stage: 'generating',
      progress: 55,
      message: 'Creating image preview...',
      currentStep: 'Rendering high-quality graphics'
    });

    // For now, create a placeholder image
    // In a real implementation, you would convert PDF to image
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 1000;
    const ctx = canvas.getContext('2d');
    
    if (ctx) {
      // Create a simple preview image
      ctx.fillStyle = template.colorScheme?.primary || '#3b82f6';
      ctx.fillRect(0, 0, canvas.width, 100);
      
      ctx.fillStyle = '#ffffff';
      ctx.font = '24px Arial';
      ctx.fillText(request.type.toUpperCase(), 50, 60);
      
      ctx.fillStyle = '#000000';
      ctx.font = '16px Arial';
      ctx.fillText(`Generated on ${new Date().toLocaleDateString()}`, 50, 150);
    }

    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => resolve(blob!), 'image/png');
    });

    const url = URL.createObjectURL(blob);

    this.updateProgress(request, {
      stage: 'formatting',
      progress: 75,
      message: 'Optimizing image quality...',
      currentStep: 'Compressing for web and print'
    });

    return {
      id: generateUUID(),
      type: 'image',
      url,
      filename: `${request.type}_${Date.now()}.png`,
      size: blob.size,
      metadata: {
        title: `${request.type} Preview`,
        createdAt: new Date().toISOString(),
        processingTime: 0
      }
    };
  }

  /**
   * Update progress and call callback
   */
  private static updateProgress(request: StreamingDocumentRequest, progress: StreamingProgress): void {
    if (request.onProgress) {
      request.onProgress(progress);
    }
  }

  /**
   * Check if generation is active
   */
  static isGenerating(generationId: string): boolean {
    return this.activeGenerations.has(generationId);
  }

  /**
   * Cancel active generation
   */
  static cancelGeneration(generationId: string): void {
    this.activeGenerations.delete(generationId);
  }

  /**
   * Get all active generations
   */
  static getActiveGenerations(): string[] {
    return Array.from(this.activeGenerations.keys());
  }
}
