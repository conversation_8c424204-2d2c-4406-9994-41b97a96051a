"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { FileImage, FileText, Download, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { generatePdf as generatePdfDocument } from "@/lib/actions/pdf-generator-ar";
import { Prisma } from "@prisma/client";

type JsonValue = Prisma.JsonValue;

interface DownloadOptionsProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: {
    id: string;
    invoiceNumber?: string | null;
    originalFileUrl?: string | null;
    [key: string]: unknown;
  };
}

// Define a type that matches what generatePdfDocument expects
interface InvoiceForPdf {
  id: string;
  invoiceNumber?: string | null;
  status?: string;
  amount?: number | null;
  currency?: string | null;
  issueDate?: Date | null;
  dueDate?: Date | null;
  vendorName?: string | null;
  notes?: string | null;
  categoryId?: string | null;
  extractedData?: JsonValue;
  lineItems?: Array<Record<string, unknown>>;
  createdAt: Date;
  updatedAt: Date;
}

export function DownloadOptions({
  isOpen,
  onOpenChange,
  invoice,
}: DownloadOptionsProps) {
  const [activeTab, setActiveTab] = useState("image");
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedFields, setSelectedFields] = useState({
    vendorInfo: true,
    customerInfo: true,
    lineItems: true,
    financialSummary: true,
    notes: true,
    metadata: false,
  });

  const handleFieldChange = (field: string, checked: boolean) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: checked,
    }));
  };

  const downloadOriginalImage = () => {
    if (!invoice.originalFileUrl) {
      toast.error("No original document available to download.");
      return;
    }

    // Create a temporary anchor element
    const link = document.createElement("a");
    link.href = invoice.originalFileUrl;

    // Extract filename from URL or use invoice number
    const filename =
      invoice.originalFileUrl.split("/").pop() ||
      `invoice-${invoice.invoiceNumber || invoice.id}.pdf`;

    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    onOpenChange(false);
  };

  const generatePdf = async () => {
    try {
      setIsGenerating(true);

      // Create an array with just this invoice and convert field selections to strings
      const invoiceArray = [invoice as unknown as InvoiceForPdf];
      const fields = Object.entries(selectedFields)
        .filter(([, selected]) => selected)
        .map(([field]) => field);

      // Pass the complete invoice object which includes extractedData.meta.language for Arabic support
      const fileBuffer = await generatePdfDocument(invoiceArray, fields);
      
      // Create a blob from the buffer
      const blob = new Blob([fileBuffer as unknown as BlobPart], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      // Create a temporary anchor element to download the file
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `invoice-${invoice.invoiceNumber || invoice.id}.pdf`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      URL.revokeObjectURL(url);

      toast.success("Your invoice PDF has been generated and downloaded.");

      onOpenChange(false);
    } catch {
      toast.error("Failed to generate PDF. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Download Invoice</DialogTitle>
          <DialogDescription>
            Choose how you want to download this invoice.
          </DialogDescription>
        </DialogHeader>

        <Tabs
          defaultValue={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="image" disabled={!invoice.originalFileUrl}>
              <FileImage className="mr-2 h-4 w-4" />
              Original Document
            </TabsTrigger>
            <TabsTrigger value="pdf">
              <FileText className="mr-2 h-4 w-4" />
              Generated PDF
            </TabsTrigger>
          </TabsList>

          <TabsContent value="image" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center mb-4">
                  <FileImage className="h-12 w-12 mx-auto text-primary/80" />
                  <h3 className="mt-2 font-medium">
                    Download Original Document
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Download the original invoice document as it was uploaded.
                  </p>
                </div>

                {!invoice.originalFileUrl && (
                  <div className="bg-amber-50 dark:bg-amber-950/20 text-amber-800 dark:text-amber-300 p-3 rounded-md text-sm mb-4">
                    No original document is available for this invoice.
                  </div>
                )}

                <Button
                  className="w-full"
                  onClick={downloadOriginalImage}
                  disabled={!invoice.originalFileUrl}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download Original
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="pdf" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center mb-4">
                  <FileText className="h-12 w-12 mx-auto text-primary/80" />
                  <h3 className="mt-2 font-medium">Generate PDF</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Create a formatted PDF with the invoice data.
                  </p>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="text-sm font-medium">Include in PDF:</div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="vendorInfo"
                        checked={selectedFields.vendorInfo}
                        onCheckedChange={(checked) =>
                          handleFieldChange("vendorInfo", checked as boolean)
                        }
                      />
                      <Label htmlFor="vendorInfo">Vendor Information</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="customerInfo"
                        checked={selectedFields.customerInfo}
                        onCheckedChange={(checked) =>
                          handleFieldChange("customerInfo", checked as boolean)
                        }
                      />
                      <Label htmlFor="customerInfo">Customer Information</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="lineItems"
                        checked={selectedFields.lineItems}
                        onCheckedChange={(checked) =>
                          handleFieldChange("lineItems", checked as boolean)
                        }
                      />
                      <Label htmlFor="lineItems">Line Items</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="financialSummary"
                        checked={selectedFields.financialSummary}
                        onCheckedChange={(checked) =>
                          handleFieldChange(
                            "financialSummary",
                            checked as boolean
                          )
                        }
                      />
                      <Label htmlFor="financialSummary">
                        Financial Summary
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="notes"
                        checked={selectedFields.notes}
                        onCheckedChange={(checked) =>
                          handleFieldChange("notes", checked as boolean)
                        }
                      />
                      <Label htmlFor="notes">Notes & Terms</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="metadata"
                        checked={selectedFields.metadata}
                        onCheckedChange={(checked) =>
                          handleFieldChange("metadata", checked as boolean)
                        }
                      />
                      <Label htmlFor="metadata">Metadata</Label>
                    </div>
                  </div>
                </div>

                <Button
                  className="w-full"
                  onClick={generatePdf}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating PDF...
                    </>
                  ) : (
                    <>
                      <FileText className="mr-2 h-4 w-4" />
                      Generate & Download PDF
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
