'use client';

import { Suspense, memo, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Overview } from '@/components/dashboard/dashboard-comp/overview';
import { RecentInvoices } from '@/components/dashboard/dashboard-comp/recent-invoices';
import { Search } from '@/components/dashboard/dashboard-comp/search';
import { InvoiceStats } from '@/components/dashboard/dashboard-comp/invoice-stats';
import { VendorDistribution } from '@/components/dashboard/dashboard-comp/vendor-distribution';
import { DashboardSkeleton } from '@/components/dashboard/dashboard-comp/dashboard-skeleton';
import { UserSubscription } from '../dashboard-comp/user-subscription';
import { UsageDashboard } from '../usage/usage-dashboard';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { AnalyticsContent } from '@/components/dashboard/analytics/analytics-dashboard';
import { DateFilterProvider } from '../providers/date-filter-provider';
import { DashboardDataProvider } from '../providers/dashboard-data-provider';
import { DateRangePicker } from '@/components/dashboard/dashboard-comp/date-range-picker';

// Memoized components for better performance
const QuickActionButton = memo(({ children, className, onClick }: { 
  children: React.ReactNode; 
  className: string; 
  onClick?: () => void;
}) => (
  <button 
    className={className}
    onClick={onClick}
  >
    {children}
  </button>
));
QuickActionButton.displayName = 'QuickActionButton';

const DashboardHeader = memo(() => (
  <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
    <div className="space-y-1">
      <h2 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 dark:from-white dark:via-blue-200 dark:to-indigo-200 bg-clip-text text-transparent">
        Dashboard
      </h2>
      <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
        Manage your invoices and track your business performance
      </p>
    </div>
    
    {/* Enhanced Controls Section */}
    <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
      <div className="flex items-center space-x-3">
        <DateRangePicker
          placeholder="Select date range"
          className="w-[280px]"
        />
        <Search />
      </div>
      
      {/* Quick Actions */}
      <div className="hidden sm:flex items-center space-x-2">
        <div className="h-6 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent dark:via-gray-600"></div>
        <QuickActionButton className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-200">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </QuickActionButton>
        <QuickActionButton className="p-2 rounded-lg bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-200">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </QuickActionButton>
      </div>
    </div>
  </div>
));
DashboardHeader.displayName = 'DashboardHeader';

const OverviewContent = memo(() => (
  <>
    <Suspense fallback={<DashboardSkeleton />}>
      <InvoiceStats />
    </Suspense>
    <Suspense fallback={<div className="h-32 bg-muted rounded-lg animate-pulse" />}>
      <UsageDashboard />
    </Suspense>
    <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-7">
      <Card
        figmaBlue
        className="col-span-1 md:col-span-2 lg:col-span-4 group hover:shadow-2xl hover:shadow-blue-500/20 dark:hover:shadow-blue-400/10 transition-all duration-500 hover:scale-[1.02]"
      >
        <CardHeader className="border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-3xl p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 shadow-sm">
              <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Cash Flow</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                Monthly income vs. expenses analysis
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
          <Suspense fallback={<div className="h-64 bg-muted rounded animate-pulse" />}>
            <Overview />
          </Suspense>
        </CardContent>
      </Card>
      <Card
        figmaPurple
        className="col-span-1 md:col-span-2 lg:col-span-3 group hover:shadow-2xl hover:shadow-purple-500/20 dark:hover:shadow-purple-400/10 transition-all duration-500 hover:scale-[1.02]"
      >
        <CardHeader className="border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-3xl p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 shadow-sm">
              <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Your Subscription</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                Current plan details and usage metrics
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10">
          <Suspense fallback={<div className="h-48 bg-muted rounded animate-pulse" />}>
            <UserSubscription />
          </Suspense>
        </CardContent>
      </Card>
    </div>
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
      <Card 
        figma 
        className="lg:col-span-3 group hover:shadow-2xl hover:shadow-teal-500/20 dark:hover:shadow-teal-400/10 transition-all duration-500 hover:scale-[1.02]"
      >
        <CardHeader className="border-b border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-r from-teal-50/50 via-white to-emerald-50/50 dark:from-teal-950/20 dark:via-gray-800/50 dark:to-emerald-950/20 rounded-t-3xl p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-teal-100 to-emerald-100 dark:from-teal-900/40 dark:to-emerald-900/40 shadow-sm">
              <svg className="w-5 h-5 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
              </svg>
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Vendor Distribution</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                Top vendors by invoice volume
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10">
          <Suspense fallback={<div className="h-64 bg-muted rounded animate-pulse" />}>
            <VendorDistribution />
          </Suspense>
        </CardContent>
      </Card>
      
      <Card 
        figmaBlue 
        className="lg:col-span-4 group hover:shadow-2xl hover:shadow-indigo-500/20 dark:hover:shadow-indigo-400/10 transition-all duration-500 hover:scale-[1.02]"
      >
        <CardHeader className="border-b border-indigo-200/30 dark:border-indigo-700/20 bg-gradient-to-r from-indigo-50/50 via-white to-blue-50/50 dark:from-indigo-950/20 dark:via-gray-800/50 dark:to-blue-950/20 rounded-t-3xl p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-indigo-100 to-blue-100 dark:from-indigo-900/40 dark:to-blue-900/40 shadow-sm">
              <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Recent Invoices</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                Latest processed invoices and transactions
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 bg-gradient-to-br from-white via-indigo-50/20 to-blue-50/20 dark:from-gray-800/50 dark:via-indigo-950/10 dark:to-blue-950/10">
          <Suspense fallback={<div className="h-64 bg-muted rounded animate-pulse" />}>
            <RecentInvoices />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  </>
));
OverviewContent.displayName = 'OverviewContent';

export default function DashboardPage() {
  const currentDate = useCallback(() => new Date().toLocaleDateString(), []);

  return (
    <DashboardLayout>
      <DateFilterProvider>
        <DashboardDataProvider>
          <div className="min-h-screen flex-col w-full">
            <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
              <DashboardHeader />

              {/* Enhanced Tabs Section */}
              <Tabs defaultValue="overview" className="space-y-6">
                <div className="flex items-center justify-between">
                  <TabsList>
                    <TabsTrigger value="overview" className="flex items-center gap-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      Overview
                    </TabsTrigger>
                    <TabsTrigger value="analytics" className="flex items-center gap-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Analytics
                    </TabsTrigger>
                  </TabsList>
                  
                  {/* Tab Info */}
                  <div className="hidden md:block text-sm text-gray-500 dark:text-gray-400 font-medium">
                    Last updated: {currentDate()}
                  </div>
                </div>
                <TabsContent value="overview" className="space-y-4">
                  <OverviewContent />
                </TabsContent>
                <TabsContent value="analytics" className="space-y-4">
                  <Suspense fallback={<DashboardSkeleton />}>
                    <AnalyticsContent />
                  </Suspense>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </DashboardDataProvider>
      </DateFilterProvider>
    </DashboardLayout>
  );
}
