'use client';

import { Suspense } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { CalendarDateRangePicker } from '@/components/dashboard/dashboard-comp/date-range-picker';
import { Overview } from '@/components/dashboard/dashboard-comp/overview';
import { RecentInvoices } from '@/components/dashboard/dashboard-comp/recent-invoices';
import { Search } from '@/components/dashboard/dashboard-comp/search';
import { InvoiceStats } from '@/components/dashboard/dashboard-comp/invoice-stats';
import { VendorDistribution } from '@/components/dashboard/dashboard-comp/vendor-distribution';
import { DashboardSkeleton } from '@/components/dashboard/dashboard-comp/dashboard-skeleton';
import { UserSubscription } from '../dashboard-comp/user-subscription';
import { UsageDashboard } from '../usage/usage-dashboard';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { AnalyticsContent } from '@/components/dashboard/analytics/analytics-dashboard';
import { DateFilterProvider } from '../providers/date-filter-provider';

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <DateFilterProvider>
        <div className="min-h-screen flex-col bg-background dark:bg-[#0B1739] w-full">
          <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
            <div className="flex flex-col items-start justify-between space-y-2 md:flex-row md:items-center md:space-y-0">
              <h2 className="text-3xl font-bold tracking-tight">
                Dashboard
              </h2>
              <div className="flex items-center space-x-2">
                <CalendarDateRangePicker />
                <Search />
              </div>
            </div>
            <Tabs defaultValue="overview" className="space-y-4">
              <TabsList className="tabs-container bg-white dark:bg-[#0B1739] border border-[#0097B1]/30 dark:border-[#0097B1]/30 rounded-xl">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
              </TabsList>
              <TabsContent value="overview" className="space-y-4">
                <Suspense fallback={<DashboardSkeleton />}>
                  <InvoiceStats />
                  <UsageDashboard />
                  <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-7">
                    <Card
                      figmaBlue
                      className="col-span-1 md:col-span-2 lg:col-span-4 group hover:shadow-2xl hover:shadow-blue-500/20 dark:hover:shadow-blue-400/10 transition-all duration-500 hover:scale-[1.02]"
                    >
                      <CardHeader className="border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-3xl p-6">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 shadow-sm">
                            <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                          </div>
                          <div>
                            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Cash Flow</CardTitle>
                            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                              Monthly income vs. expenses analysis
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
                        <Overview />
                      </CardContent>
                    </Card>
                    <Card
                      figmaPurple
                      className="col-span-1 md:col-span-2 lg:col-span-3 group hover:shadow-2xl hover:shadow-purple-500/20 dark:hover:shadow-purple-400/10 transition-all duration-500 hover:scale-[1.02]"
                    >
                      <CardHeader className="border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-3xl p-6">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 shadow-sm">
                            <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                            </svg>
                          </div>
                          <div>
                            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Your Subscription</CardTitle>
                            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                              Current plan details and usage metrics
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10">
                        <UserSubscription />
                      </CardContent>
                    </Card>
                  </div>
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
                    <Card 
                      figma 
                      className="lg:col-span-3 group hover:shadow-2xl hover:shadow-teal-500/20 dark:hover:shadow-teal-400/10 transition-all duration-500 hover:scale-[1.02]"
                    >
                      <CardHeader className="border-b border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-r from-teal-50/50 via-white to-emerald-50/50 dark:from-teal-950/20 dark:via-gray-800/50 dark:to-emerald-950/20 rounded-t-3xl p-6">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-xl bg-gradient-to-br from-teal-100 to-emerald-100 dark:from-teal-900/40 dark:to-emerald-900/40 shadow-sm">
                            <svg className="w-5 h-5 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                            </svg>
                          </div>
                          <div>
                            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Vendor Distribution</CardTitle>
                            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                              Top vendors by invoice volume
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10">
                        <VendorDistribution />
                      </CardContent>
                    </Card>
                    
                    <Card 
                      figmaBlue 
                      className="lg:col-span-4 group hover:shadow-2xl hover:shadow-indigo-500/20 dark:hover:shadow-indigo-400/10 transition-all duration-500 hover:scale-[1.02]"
                    >
                      <CardHeader className="border-b border-indigo-200/30 dark:border-indigo-700/20 bg-gradient-to-r from-indigo-50/50 via-white to-blue-50/50 dark:from-indigo-950/20 dark:via-gray-800/50 dark:to-blue-950/20 rounded-t-3xl p-6">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-xl bg-gradient-to-br from-indigo-100 to-blue-100 dark:from-indigo-900/40 dark:to-blue-900/40 shadow-sm">
                            <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                          <div>
                            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Recent Invoices</CardTitle>
                            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                              Latest processed invoices and transactions
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6 bg-gradient-to-br from-white via-indigo-50/20 to-blue-50/20 dark:from-gray-800/50 dark:via-indigo-950/10 dark:to-blue-950/10">
                        <RecentInvoices />
                      </CardContent>
                    </Card>
                  </div>
                </Suspense>
              </TabsContent>
              <TabsContent value="analytics" className="space-y-4">
                <Suspense fallback={<DashboardSkeleton />}>
                  <AnalyticsContent />
                </Suspense>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </DateFilterProvider>
    </DashboardLayout>
  );
}
