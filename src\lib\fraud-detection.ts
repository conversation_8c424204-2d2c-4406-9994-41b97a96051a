import { prisma } from "@/lib/prisma";
import { AuditIssue, InvoiceData } from "@/types/invoice";
import { 
  FraudDetectionRule, 
} from "@/types/enhanced-invoice";
import { toast } from "sonner";

/**
 * Perform comprehensive fraud detection on an invoice
 */
export async function detectFraud(
  invoiceData: InvoiceData,
  userId: string
): Promise<{ 
  fraudScore: number;
  issues: AuditIssue[];
  status: 'PASS' | 'WARNING' | 'FAIL';
}> {
  try {
    // Get user's custom fraud detection rules
    const aiSettings = await prisma.aISettings.findUnique({
      where: { userId }
    });
    
    const customRules = aiSettings?.fraudDetectionRules as unknown as FraudDetectionRule[] || [];
    
    // Get vendor's historical invoices for comparison
    const vendorName = invoiceData.vendor?.name;
    let historicalInvoices: InvoiceData[] = [];
    
    if (vendorName) {
      const rawInvoices = await prisma.invoice.findMany({
        where: {
          userId,
          vendorName,
          id: { not: invoiceData.id } // Exclude current invoice
        },
        include: {
          lineItems: true
        },
        orderBy: { issueDate: 'desc' },
        take: 10 // Last 10 invoices
      });
      
      historicalInvoices = rawInvoices as unknown as InvoiceData[];
    }
    
    // Run standard fraud detection checks
    const standardIssues = runStandardFraudChecks(invoiceData, historicalInvoices);
    
    // Run custom rules
    const customIssues = runCustomRules(invoiceData, customRules);
    
    // Combine all issues
    const allIssues = [...standardIssues, ...customIssues];
    
    // Calculate fraud score (0-100, higher means more suspicious)
    const fraudScore = calculateFraudScore(allIssues);
    
    // Determine overall status
    let status: 'PASS' | 'WARNING' | 'FAIL' = 'PASS';
    if (fraudScore > 70) {
      status = 'FAIL';
    } else if (fraudScore > 30) {
      status = 'WARNING';
    }
    
    return {
      fraudScore,
      issues: allIssues,
      status
    };
  } catch {
    return {
      fraudScore: 0,
      issues: [],
      status: 'PASS'
    };
  }
}

/**
 * Run standard fraud detection checks
 */
function runStandardFraudChecks(
  invoiceData: InvoiceData,
  historicalInvoices: InvoiceData[]
): AuditIssue[] {
  const issues: AuditIssue[] = [];
  
  // Check 1: Mathematical consistency
  if (invoiceData.financials) {
    const { subtotal, tax, total } = invoiceData.financials;
    
    if (subtotal && tax && total) {
      const parsedSubtotal = parseFloat(subtotal);
      const parsedTax = parseFloat(tax);
      const parsedTotal = parseFloat(total);
      
      if (!isNaN(parsedSubtotal) && !isNaN(parsedTax) && !isNaN(parsedTotal)) {
        const calculatedTotal = parsedSubtotal + parsedTax;
        const tolerance = 0.01; // 1 cent tolerance for rounding errors
        
        if (Math.abs(calculatedTotal - parsedTotal) > tolerance) {
          issues.push({
            type: 'CALCULATION_ERROR',
            severity: 'HIGH',
            description: `Total amount (${parsedTotal}) does not match subtotal (${parsedSubtotal}) + tax (${parsedTax})`,
            affectedFields: ['financials.subtotal', 'financials.tax', 'financials.total']
          });
        }
      }
    }
  }
  
  // Check 2: Line item consistency
  if (invoiceData.lineItems && invoiceData.lineItems.length > 0 && invoiceData.financials?.subtotal) {
    const lineItemTotal = invoiceData.lineItems.reduce((sum, item) => {
      const amount = parseFloat(item.amount || '0');
      return sum + (isNaN(amount) ? 0 : amount);
    }, 0);
    
    const parsedSubtotal = parseFloat(invoiceData.financials.subtotal);
    
    if (!isNaN(parsedSubtotal)) {
      const tolerance = 0.01 * invoiceData.lineItems.length; // Allow for rounding errors
      
      if (Math.abs(lineItemTotal - parsedSubtotal) > tolerance) {
        issues.push({
          type: 'LINE_ITEM_MISMATCH',
          severity: 'MEDIUM',
          description: `Sum of line items (${lineItemTotal.toFixed(2)}) does not match subtotal (${parsedSubtotal})`,
          affectedFields: ['lineItems', 'financials.subtotal']
        });
      }
    }
  }
  
  // Check 3: Duplicate invoice number
  if (invoiceData.invoiceNumber && historicalInvoices.length > 0) {
    const duplicateInvoice = historicalInvoices.find(
      inv => inv.invoiceNumber === invoiceData.invoiceNumber
    );
    
    if (duplicateInvoice) {
      issues.push({
        type: 'DUPLICATE_INVOICE',
        severity: 'HIGH',
        description: `Invoice number ${invoiceData.invoiceNumber} already exists for this vendor`,
        affectedFields: ['invoiceNumber']
      });
    }
  }
  
  // Check 4: Unusual amount compared to historical invoices
  if (invoiceData.financials?.total && historicalInvoices.length >= 3) {
    const currentAmount = parseFloat(invoiceData.financials.total);
    
    if (!isNaN(currentAmount)) {
      const historicalAmounts = historicalInvoices
        .map(inv => typeof inv.amount === 'number' ? inv.amount : null)
        .filter((amount): amount is number => amount !== null && !isNaN(amount));
      
      if (historicalAmounts.length >= 3) {
        const avgAmount = historicalAmounts.reduce((sum, amount) => sum + amount, 0) / historicalAmounts.length;
        const stdDev = Math.sqrt(
          historicalAmounts.reduce((sum, amount) => sum + Math.pow(amount - avgAmount, 2), 0) / historicalAmounts.length
        );
        
        // Check if current amount is more than 3 standard deviations from the mean
        if (Math.abs(currentAmount - avgAmount) > 3 * stdDev) {
          issues.push({
            type: 'UNUSUAL_AMOUNT',
            severity: 'MEDIUM',
            description: `Invoice amount (${currentAmount.toFixed(2)}) is significantly different from historical average (${avgAmount.toFixed(2)})`,
            affectedFields: ['financials.total']
          });
        }
      }
    }
  }
  
  // Check 5: Round numbers (often indicate estimates rather than actual charges)
  if (invoiceData.financials?.total) {
    const total = parseFloat(invoiceData.financials.total);
    
    if (!isNaN(total) && total >= 1000 && total % 100 === 0) {
      issues.push({
        type: 'ROUND_AMOUNT',
        severity: 'LOW',
        description: `Invoice total (${total.toFixed(2)}) is a suspiciously round number`,
        affectedFields: ['financials.total']
      });
    }
  }
  
  // Check 6: Future-dated invoice
  if (invoiceData.date) {
    const invoiceDate = new Date(invoiceData.date);
    const today = new Date();
    
    if (invoiceDate > today) {
      issues.push({
        type: 'FUTURE_DATE',
        severity: 'MEDIUM',
        description: `Invoice date (${invoiceData.date}) is in the future`,
        affectedFields: ['date']
      });
    }
  }
  
  return issues;
}

/**
 * Run custom fraud detection rules
 */
function runCustomRules(
  invoiceData: InvoiceData,
  rules: FraudDetectionRule[]
): AuditIssue[] {
  const issues: AuditIssue[] = [];
  
  // Process each enabled rule
  for (const rule of rules) {
    if (!rule.enabled) continue;
    
    try {
      // Simple rule evaluation - in a real system, this would be more sophisticated
      // This is a simplified example using string conditions
      const condition = rule.condition;
      
      if (condition.includes('amount>') && invoiceData.financials?.total) {
        const threshold = parseFloat(condition.split('amount>')[1]);
        const total = parseFloat(invoiceData.financials.total);
        
        if (!isNaN(threshold) && !isNaN(total) && total > threshold) {
          issues.push({
            type: 'CUSTOM_RULE',
            severity: rule.severity,
            description: rule.description,
            affectedFields: ['financials.total']
          });
        }
      }
      
      // Add more condition types as needed
      
    } catch {
      toast.error(`Error evaluating rule ${rule.name}`);
    }
  }
  
  return issues;
}

/**
 * Calculate fraud score based on detected issues
 */
function calculateFraudScore(issues: AuditIssue[]): number {
  if (issues.length === 0) return 0;
  
  // Assign weights to different severity levels
  const severityWeights = {
    'LOW': 10,
    'MEDIUM': 25,
    'HIGH': 50
  };
  
  // Calculate weighted sum of issues
  const weightedSum = issues.reduce((sum, issue) => {
    return sum + severityWeights[issue.severity];
  }, 0);
  
  // Cap at 100
  return Math.min(100, weightedSum);
}
