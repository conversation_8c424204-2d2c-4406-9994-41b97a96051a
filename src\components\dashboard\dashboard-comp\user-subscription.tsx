'use client';

import { Button } from '@/components/ui/button';
import {
  BadgeCheck,
  Zap,
  FileText,
  MessageSquare,
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface SubscriptionData {
  id: string;
  status: string;
  plan: {
    name: string;
    price: string;
    currency: string;
    billingInterval: string;
  };
  startDate: string | null;
  endDate: string | null;
  renewalDate: string | null;
  isActive: boolean;
}

interface UsageStats {
  chatUsage: number;
  invoiceUsage: number;
  chatLimit: number;
  invoiceLimit: number;
  resetDate: Date;
  daysUntilReset: number;
}

export function UserSubscription() {
  const [subscriptionData, setSubscriptionData] =
    useState<SubscriptionData | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [usageLoading, setUsageLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch subscription data
        const subscriptionResponse = await fetch(
          '/api/subscriptions/active'
        );
        if (subscriptionResponse.ok) {
          const subscriptionData = await subscriptionResponse.json();
          setSubscriptionData(subscriptionData);
        }

        // Fetch usage stats
        const usageResponse = await fetch('/api/usage/stats');
        if (usageResponse.ok) {
          const usageResult = await usageResponse.json();
          if (usageResult.success) {
            setUsageStats({
              ...usageResult.stats,
              resetDate: new Date(usageResult.stats.resetDate),
            });
          }
        }
      } finally {
        setIsLoading(false);
        setUsageLoading(false);
      }
    };

    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-6 bg-muted rounded w-32 mb-4"></div>
          <div className="space-y-4">
            <div className="h-4 bg-muted rounded w-full"></div>
            <div className="h-2 bg-muted rounded w-full"></div>
            <div className="h-4 bg-muted rounded w-full"></div>
            <div className="h-2 bg-muted rounded w-full"></div>
          </div>
        </div>
      </div>
    );
  }

  const planName = subscriptionData?.plan?.name || 'No Subscription';

  // Calculate usage percentages
  const invoicePercentage =
    usageStats && usageStats.invoiceLimit > 0
      ? (usageStats.invoiceUsage / usageStats.invoiceLimit) * 100
      : 0;

  const chatPercentage =
    usageStats && usageStats.chatLimit > 0
      ? (usageStats.chatUsage / usageStats.chatLimit) * 100
      : 0;

  return (
    <div className="space-y-6">
      {/* Enhanced plan header with gradient background */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 p-6 border border-purple-200/30 dark:border-purple-700/20">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-transparent to-indigo-500/5 dark:from-purple-400/10 dark:via-transparent dark:to-indigo-400/10" />
        
        <div className="relative z-10 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 shadow-sm">
              <BadgeCheck className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h3 className="font-bold text-gray-900 dark:text-gray-100 text-lg">
                {planName}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Current subscription plan
              </p>
            </div>
          </div>
          <Button
            size="sm"
            className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-purple-500/25 transition-all duration-200 px-6 rounded-xl font-semibold"
            onClick={() =>
              (window.location.href = '/dashboard/subscription')
            }
          >
            <Zap className="h-4 w-4 mr-2" />
            Upgrade
          </Button>
        </div>
      </div>

      {usageLoading ? (
        <div className="space-y-4">
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-2 bg-muted rounded w-full"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
          </div>
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-2 bg-muted rounded w-full"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
          </div>
        </div>
      ) : usageStats ? (
        <>
          {/* Enhanced Invoice Processing Usage */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 p-5 border border-blue-200/30 dark:border-blue-700/20">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 dark:from-blue-400/10 dark:via-transparent dark:to-cyan-400/10" />
            
            <div className="relative z-10 space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 shadow-sm">
                    <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                      Invoice Processing
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Monthly document processing
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-gray-900 dark:text-gray-100">
                    {usageStats.invoiceUsage.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    of {usageStats.invoiceLimit.toLocaleString()}
                  </div>
                </div>
              </div>
              
              {/* Enhanced progress bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-xs font-medium">
                  <span className="text-gray-600 dark:text-gray-400">
                    {Math.round(invoicePercentage)}% used
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {usageStats.invoiceLimit - usageStats.invoiceUsage} remaining
                  </span>
                </div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transition-all duration-500 shadow-sm"
                    style={{
                      width: `${Math.min(invoicePercentage, 100)}%`,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Chat Messages Usage */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-50/50 via-white to-teal-50/50 dark:from-emerald-950/20 dark:via-gray-800/50 dark:to-teal-950/20 p-5 border border-emerald-200/30 dark:border-emerald-700/20">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-teal-500/5 dark:from-emerald-400/10 dark:via-transparent dark:to-teal-400/10" />
            
            <div className="relative z-10 space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/40 dark:to-teal-900/40 shadow-sm">
                    <MessageSquare className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                      Chat Messages
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      AI chat interactions
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-gray-900 dark:text-gray-100">
                    {usageStats.chatUsage.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    of {usageStats.chatLimit.toLocaleString()}
                  </div>
                </div>
              </div>
              
              {/* Enhanced progress bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-xs font-medium">
                  <span className="text-gray-600 dark:text-gray-400">
                    {Math.round(chatPercentage)}% used
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {usageStats.chatLimit - usageStats.chatUsage} remaining
                  </span>
                </div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full transition-all duration-500 shadow-sm"
                    style={{ width: `${Math.min(chatPercentage, 100)}%` }}
                  />
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="text-center py-4">
          <p className="text-muted-foreground text-sm">
            Unable to load usage statistics
          </p>
        </div>
      )}

      {/* Enhanced upgrade CTA */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-amber-50/50 via-orange-50/50 to-yellow-50/50 dark:from-amber-950/20 dark:via-orange-950/20 dark:to-yellow-950/20 p-6 border border-amber-200/30 dark:border-amber-700/20">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-500/5 via-orange-500/5 to-yellow-500/5 dark:from-amber-400/10 dark:via-orange-400/10 dark:to-yellow-400/10" />
        
        <div className="relative z-10 flex items-start gap-4">
          <div className="p-3 rounded-xl bg-gradient-to-br from-amber-100 to-orange-100 dark:from-amber-900/40 dark:to-orange-900/40 shadow-sm">
            <Zap className="h-6 w-6 text-amber-600 dark:text-amber-400" />
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
              Need more resources?
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Upgrade to our Business plan for higher limits and advanced features.
            </p>
            <Button
              size="sm"
              className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg hover:shadow-amber-500/25 transition-all duration-200 rounded-xl font-semibold"
              onClick={() =>
                (window.location.href = '/dashboard/subscription')
              }
            >
              View Plans
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
