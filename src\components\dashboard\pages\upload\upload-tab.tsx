"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { FileUploadZone } from "./file-upload-zone";

interface ProcessingFile {
  file: File;
  progress: number;
  status: 'pending' | 'processing' | 'completed' | 'error' | 'duplicate';
  error?: string;
  previewUrl?: string;
}

interface UploadTabProps {
  form: UseFormReturn<{ files: File[] }>;
  processingFiles: ProcessingFile[];
  isProcessing: boolean;
  maxFiles: number;
  onAddFiles: (files: File[]) => void;
  onRemoveFile: (index: number) => void;
  onClear: () => void;
  onViewProcessing: () => void;
}

export function UploadTab({
  form,
  processingFiles,
  isProcessing,
  maxFiles,
  onAddFiles,
  onRemoveFile,
  onClear,
  onViewProcessing
}: UploadTabProps) {
  return (
    <Form {...form}>
      <form className="space-y-6">
        <FormField
          control={form.control}
          name="files"
          render={() => (
            <FormItem>
              <FormControl>
                <FileUploadZone
                  files={processingFiles}
                  isProcessing={isProcessing}
                  maxFiles={maxFiles}
                  onAddFiles={onAddFiles}
                  onRemoveFile={onRemoveFile}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-4 mt-8">
          {processingFiles.length > 0 && (
            <Button
              type="button"
              onClick={onViewProcessing}
              className="w-full sm:w-auto bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0 shadow-lg rounded-lg font-medium cursor-pointer"
            >
              View Processing Status
            </Button>
          )}
          <Button
            type="button"
            variant="outline"
            onClick={onClear}
            className="w-full sm:w-auto border border-gray-200/50 dark:border-gray-700/50 text-gray-700 dark:text-gray-300 rounded-lg font-medium cursor-pointer"
            disabled={processingFiles.length === 0 || isProcessing}
          >
            Clear All
          </Button>
        </div>
      </form>
    </Form>
  );
} 