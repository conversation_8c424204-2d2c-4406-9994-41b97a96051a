'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface UltraSmartDataHandlerProps {
  chatId: string;
  onDocumentGenerated?: (documents: any[]) => void;
  onActionExecuted?: (action: any) => void;
  onStatsUpdate?: (stats: any) => void;
  onProgressUpdate?: (progress: any) => void;
}

/**
 * Ultra-Smart Data Stream Handler
 * Handles streaming data from the ultra-smart chat endpoint
 */
export function UltraSmartDataHandler({
  chatId,
  onDocumentGenerated,
  onActionExecuted,
  onStatsUpdate,
  onProgressUpdate
}: UltraSmartDataHandlerProps) {
  const router = useRouter();

  useEffect(() => {
    // Set up EventSource for streaming data
    const eventSource = new EventSource(`/api/chat/ultra-smart/stream?chatId=${chatId}`);

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case 'document_progress':
            if (onProgressUpdate) {
              onProgressUpdate(data.data);
            }
            toast.info(`${data.data.stage}: ${data.data.message} (${data.data.progress}%)`);
            break;

          case 'documents_generated':
            if (onDocumentGenerated) {
              onDocumentGenerated(data.data.documents);
            }
            toast.success(`${data.data.documents.length} document(s) generated!`);
            break;

          case 'action_result':
            if (onActionExecuted) {
              onActionExecuted(data.data);
            }
            
            // Handle UI actions
            if (data.data.uiActions) {
              handleUIActions(data.data.uiActions);
            }
            break;

          case 'completion':
            if (onStatsUpdate) {
              onStatsUpdate(data.data);
            }
            break;

          case 'document_error':
            toast.error(`Document generation failed: ${data.data.message}`);
            break;

          case 'action_error':
            toast.error(`Action failed: ${data.data.message}`);
            break;
        }
      } catch (error) {
        console.error('Failed to parse streaming data:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('EventSource error:', error);
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, [chatId, onDocumentGenerated, onActionExecuted, onStatsUpdate, onProgressUpdate]);

  /**
   * Handle UI actions from the AI agent
   */
  const handleUIActions = (actions: any[]) => {
    actions.forEach((action, index) => {
      setTimeout(() => {
        switch (action.type) {
          case 'toast':
            const toastType = action.payload.type || 'info';
            if (toastType === 'success') {
              toast.success(action.payload.message);
            } else if (toastType === 'error') {
              toast.error(action.payload.message);
            } else {
              toast.info(action.payload.message);
            }
            break;

          case 'navigate':
            if (action.target) {
              router.push(action.target);
            }
            break;

          case 'download':
            if (action.payload.url) {
              const link = document.createElement('a');
              link.href = action.payload.url;
              link.download = action.payload.filename || 'download';
              link.click();
            }
            break;

          case 'refresh':
            if (action.target === 'invoice-list') {
              window.dispatchEvent(new CustomEvent('refresh-invoices'));
            } else if (action.target === 'page') {
              window.location.reload();
            }
            break;

          case 'modal':
            // Handle modal opening
            if (action.payload.type === 'invoice-details' && action.payload.invoiceId) {
              router.push(`/dashboard/invoices/${action.payload.invoiceId}`);
            }
            break;

          case 'update':
            // Handle data updates
            if (action.target) {
              window.dispatchEvent(new CustomEvent(`update-${action.target}`, {
                detail: action.payload
              }));
            }
            break;
        }
      }, (action.delay || 0) + index * 500);
    });
  };

  return null; // This component doesn't render anything
}

/**
 * Hook for using ultra-smart chat data
 */
export function useUltraSmartChat(chatId: string) {
  const [documents, setDocuments] = useState<any[]>([]);
  const [stats, setStats] = useState({
    totalTokens: 0,
    totalCost: 0,
    avgResponseTime: 0,
    actionsExecuted: 0
  });
  const [progress, setProgress] = useState<any>(null);

  const handleDocumentGenerated = (newDocuments: any[]) => {
    setDocuments(prev => [...prev, ...newDocuments]);
  };

  const handleActionExecuted = (action: any) => {
    setStats(prev => ({
      ...prev,
      actionsExecuted: prev.actionsExecuted + 1
    }));
  };

  const handleStatsUpdate = (newStats: any) => {
    setStats(prev => ({
      totalTokens: prev.totalTokens + (newStats.tokenUsage || 0),
      totalCost: prev.totalCost + (newStats.estimatedCost || 0),
      avgResponseTime: newStats.responseTime || prev.avgResponseTime,
      actionsExecuted: prev.actionsExecuted
    }));
  };

  const handleProgressUpdate = (progressData: any) => {
    setProgress(progressData);
  };

  return {
    documents,
    stats,
    progress,
    handlers: {
      onDocumentGenerated: handleDocumentGenerated,
      onActionExecuted: handleActionExecuted,
      onStatsUpdate: handleStatsUpdate,
      onProgressUpdate: handleProgressUpdate
    }
  };
}


