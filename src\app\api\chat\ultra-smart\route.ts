import {
  type Message,
  createDataStreamResponse,
  streamText,
} from 'ai';
import { myProvider, selectOptimalModel } from '@/lib/ai/models';
import { ContextEngine, generateContextualPrompt } from '@/lib/ai/context-engine';
import { ActionEngine, detectIntent } from '@/lib/ai/action-engine';
import { MemoryEngine } from '@/lib/ai/memory-engine';
import { CacheEngine } from '@/lib/ai/cache-engine';
import { PerformanceMonitor } from '@/lib/ai/performance-monitor';
import { DocumentGenerationEngine } from '@/lib/ai/document-generation-engine';
import { uploadToVercelBlob } from '@/lib/blob';
import { getCurrentUserId } from '@/lib/clerk-helpers';
import { generateUUID } from '@/lib/utils';
import db from '@/db/db';

interface UltraSmartChatRequest {
  messages: Message[];
  chatId: string;
  context?: {
    page?: string;
    userIntent?: string;
    previousActions?: string[];
  };
  options?: {
    generateDocuments?: boolean;
    includeInsights?: boolean;
    format?: 'pdf' | 'excel' | 'image' | 'all';
    smartMode?: boolean;
    learningEnabled?: boolean;
  };
}

/**
 * Ultra-Smart Chat API - The most advanced AI agent endpoint
 * Features:
 * - Intelligent context compression
 * - Multi-layer memory system
 * - Smart action execution
 * - Document generation
 * - User learning and adaptation
 * - Cost optimization
 */
export async function POST(request: Request) {
  const startTime = Date.now();
  let userId: string;
  
  try {
    userId = await getCurrentUserId();
    if (!userId) {
      return new Response('Unauthorized', { status: 401 });
    }

    const body: UltraSmartChatRequest = await request.json();
    const { messages, chatId, context, options } = body;
    
    if (!messages || messages.length === 0) {
      return new Response('Messages are required', { status: 400 });
    }

    const lastMessage = messages[messages.length - 1];
    const userMessage = lastMessage.content as string;

    // ===== SMART CONTEXT GENERATION =====
    
    // Get user learning profile for context optimization
    const learningProfile = await CacheEngine.getUserLearningProfile(userId);
    const smartSuggestions = await CacheEngine.getSmartContextSuggestions(userId);
    
    // Get conversation memory
    const conversationMemory = await CacheEngine.getConversationMemory(chatId);
    
    // Detect intent with enhanced context
    const intentAnalysis = detectIntent(userMessage);
    const enhancedIntent = {
      ...intentAnalysis,
      userExpertise: learningProfile?.contextPreferences.detailLevel || 'detailed',
      preferredFormats: smartSuggestions.suggestedFormats,
      recentActions: conversationMemory?.context.recentActions || []
    };

    // Get optimized context based on intent and user learning
    const userContext = await ContextEngine.getUserContext(
      userId, 
      context?.page, 
      enhancedIntent.intent
    );

    // ===== SMART MODEL SELECTION =====
    
    const optimalModel = selectOptimalModel(userMessage, userContext);
    
    // ===== ENHANCED PROMPT GENERATION =====
    
    const systemPrompt = generateEnhancedPrompt(
      userContext, 
      conversationMemory, 
      learningProfile,
      smartSuggestions
    );

    // ===== STREAMING RESPONSE WITH ACTIONS =====
    
    return createDataStreamResponse({
      execute: async (dataStream) => {
        const result = await streamText({
          model: myProvider(optimalModel),
          system: systemPrompt,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          onFinish: async (completion) => {
            const responseTime = Date.now() - startTime;
            
            // ===== EXECUTE ACTIONS =====
            
            let actionResults = null;
            let generatedDocuments = null;
            
            if (enhancedIntent.confidence > 0.7) {
              try {
                actionResults = await ActionEngine.executeAction(
                  enhancedIntent.intent,
                  enhancedIntent.parameters,
                  userId
                );
                
                if (actionResults.documents) {
                  generatedDocuments = actionResults.documents;
                }
                
                // Send action results to client
                dataStream.writeData({
                  type: 'action_result',
                  data: actionResults
                });
                
              } catch (error) {
                console.error('Action execution failed:', error);
                dataStream.writeData({
                  type: 'action_error',
                  data: { message: 'Failed to execute action' }
                });
              }
            }

            // ===== DOCUMENT GENERATION =====
            
            if (options?.generateDocuments && enhancedIntent.intent.includes('create')) {
              try {
                const documents = await DocumentGenerationEngine.generateDocument(userId, {
                  type: 'invoice',
                  format: options.format || learningProfile?.behaviorPatterns.documentPreferences.format || 'pdf',
                  data: actionResults?.data,
                  options: {
                    includeCharts: true,
                    includeImages: true,
                    includeBranding: true,
                    quality: 'high',
                    language: learningProfile?.contextPreferences.preferredLanguage || 'en',
                    rtl: false
                  }
                });
                
                dataStream.writeData({
                  type: 'documents_generated',
                  data: { documents }
                });
                
              } catch (error) {
                console.error('Document generation failed:', error);
              }
            }

            // ===== LEARNING AND MEMORY UPDATE =====
            
            if (options?.learningEnabled !== false) {
              // Learn from this interaction
              await CacheEngine.learnFromInteraction(
                userId,
                enhancedIntent.intent,
                actionResults?.success || false,
                responseTime,
                {
                  documentFormat: options?.format,
                  page: context?.page,
                  messageLength: userMessage.length
                }
              );
              
              // Update conversation memory
              await CacheEngine.updateConversationMemory(chatId, {
                userId,
                summary: completion.text.substring(0, 200),
                keyTopics: [enhancedIntent.intent, ...(conversationMemory?.keyTopics || [])].slice(0, 10),
                messageCount: (conversationMemory?.messageCount || 0) + 1,
                context: {
                  recentActions: [enhancedIntent.intent, ...(conversationMemory?.context.recentActions || [])].slice(0, 5),
                  frequentRequests: smartSuggestions.preferredActions,
                  userExpertise: learningProfile?.contextPreferences.detailLevel as any || 'detailed',
                  preferredFormats: smartSuggestions.suggestedFormats
                }
              });
            }

            // ===== SAVE MESSAGE TO DATABASE =====
            
            try {
              await db.message.create({
                data: {
                  id: generateUUID(),
                  chatId,
                  userId,
                  role: 'assistant',
                  content: completion.text,
                  metadata: {
                    model: optimalModel,
                    responseTime,
                    tokenUsage: completion.usage?.totalTokens || 0,
                    actionExecuted: enhancedIntent.intent,
                    documentsGenerated: generatedDocuments?.length || 0
                  },
                  createdAt: new Date()
                }
              });
            } catch (error) {
              console.error('Failed to save message:', error);
            }

            // ===== PERFORMANCE MONITORING =====
            
            PerformanceMonitor.recordMetrics({
              responseTime,
              tokenUsage: completion.usage?.totalTokens || 0,
              queryType: enhancedIntent.intent
            });

            // Send completion signal
            dataStream.writeData({
              type: 'completion',
              data: {
                responseTime,
                tokenUsage: completion.usage?.totalTokens || 0,
                actionExecuted: enhancedIntent.intent,
                success: true
              }
            });
          }
        });

        result.pipeDataStreamToResponse(dataStream);
      }
    });

  } catch (error) {
    console.error('Ultra-smart chat error:', error);
    
    // Record error metrics
    PerformanceMonitor.recordMetrics({
      responseTime: Date.now() - startTime,
      tokenUsage: 0,
      queryType: 'error'
    });

    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Generate enhanced system prompt with user learning and memory
 */
function generateEnhancedPrompt(
  context: any,
  conversationMemory: any,
  learningProfile: any,
  smartSuggestions: any
): string {
  const basePrompt = generateContextualPrompt(context);
  
  const memoryContext = conversationMemory ? `

CONVERSATION MEMORY:
- Previous topics: ${conversationMemory.keyTopics.join(', ')}
- Recent actions: ${conversationMemory.context.recentActions.join(', ')}
- Message count: ${conversationMemory.messageCount}
- User expertise: ${conversationMemory.context.userExpertise}` : '';

  const learningContext = learningProfile ? `

USER LEARNING PROFILE:
- Preferred actions: ${learningProfile.behaviorPatterns.preferredActions.join(', ')}
- Document preferences: ${learningProfile.behaviorPatterns.documentPreferences.format} format, ${learningProfile.behaviorPatterns.documentPreferences.style} style
- Response style: ${learningProfile.contextPreferences.responseStyle}
- Detail level: ${learningProfile.contextPreferences.detailLevel}
- Success rate: ${Math.round((learningProfile.performanceMetrics.successfulActions / Math.max(learningProfile.performanceMetrics.totalInteractions, 1)) * 100)}%` : '';

  const smartContext = `

SMART SUGGESTIONS:
- Recommended actions: ${smartSuggestions.preferredActions.join(', ')}
- Suggested formats: ${smartSuggestions.suggestedFormats.join(', ')}
- Context level: ${smartSuggestions.contextLevel}`;

  return `${basePrompt}${memoryContext}${learningContext}${smartContext}

ENHANCED CAPABILITIES:
You can now execute complex actions, generate documents, create spreadsheets, and perform UI operations. When users request actions:

1. **Document Generation**: Create professional PDFs, Excel spreadsheets, or images with custom branding
2. **UI Actions**: Navigate pages, show modals, display toasts, trigger downloads
3. **Smart Workflows**: Execute multi-step processes automatically
4. **Learning**: Adapt to user preferences and improve over time

Always prioritize user preferences from their learning profile and conversation memory.`;
}
