'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';

// Import smaller components
import { StatusCard } from './status/status-card';
import { SystemOverview } from './status/system-overview';
import { PerformanceMetrics } from './status/performance-metrics';
import { IntelligenceMetrics } from './status/intelligence-metrics';
import { CacheMetrics } from './status/cache-metrics';
import { UserProfileMetrics } from './status/user-profile-metrics';
import { CapabilitiesOverview } from './status/capabilities-overview';

interface SystemStatus {
  overall: 'excellent' | 'good' | 'fair' | 'poor';
  components: {
    performance: {
      status: 'excellent' | 'good' | 'fair' | 'poor';
      avgResponseTime: number;
      successRate: number;
      recommendations: string[];
    };
    cache: {
      status: 'excellent' | 'good' | 'fair' | 'poor';
      hitRate: number;
      size: number;
      recommendations: string[];
    };
    intelligence: {
      status: 'excellent' | 'good' | 'fair' | 'poor';
      contextAccuracy: number;
      predictionConfidence: number;
      recommendations: string[];
    };
  };
  userSpecific?: {
    profileCompleteness: number;
    conversationQuality: number;
    businessInsights: number;
    recommendations: string[];
  };
  systemHealth: {
    uptime: string;
    version: string;
    lastOptimization: string;
    nextMaintenance: string;
  };
}

export function SystemStatus() {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSystemStatus();
  }, []);

  const fetchSystemStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/ai/status');
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      } else {
        throw new Error('Failed to fetch system status');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'fair': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Handle loading and error states
  if (loading || error || !status) {
    return <StatusCard loading={loading} error={error} onRetry={fetchSystemStatus} />;
  }

  return (
    <div className="space-y-6">
      {/* Overall Status */}
      <SystemOverview status={status} />

      {/* Detailed Status */}
      <Tabs defaultValue="performance" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="intelligence">Intelligence</TabsTrigger>
          <TabsTrigger value="cache">Caching</TabsTrigger>
          <TabsTrigger value="user">User Profile</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <PerformanceMetrics performance={status.components.performance} getStatusColor={getStatusColor} />
        </TabsContent>

        <TabsContent value="intelligence" className="space-y-4">
          <IntelligenceMetrics intelligence={status.components.intelligence} getStatusColor={getStatusColor} />
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <CacheMetrics cache={status.components.cache} getStatusColor={getStatusColor} />
        </TabsContent>

        <TabsContent value="user" className="space-y-4">
          <UserProfileMetrics userSpecific={status.userSpecific} />
        </TabsContent>
      </Tabs>

      {/* Capabilities Overview */}
      <CapabilitiesOverview />
    </div>
  );
}
