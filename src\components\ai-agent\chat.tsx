'use client';

import type { Attachment, Message } from 'ai';
import { useChat } from 'ai/react';
import { useState, memo } from 'react';
import useSWR, { useSWRConfig } from 'swr';

import { ChatHeader } from '@/components/ai-agent/chat-header';
import type { Vote } from '@prisma/client';
import { fetcher, generateUUID } from '@/lib/utils';

import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import { VisibilityType } from '@/hooks/use-chat-visibility';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';

const PureChat = memo(function Chat({
  id,
  initialMessages,
  selectedChatModel,
  selectedVisibilityType,
  isReadonly,
}: {
  id: string;
  initialMessages: Array<Message>;
  selectedChatModel: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
}) {
  const { mutate } = useSWRConfig();
  const router = useRouter();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    isLoading,
    stop,
    reload,
    data,
  } = useChat({
    id,
    api: '/api/chat/smart', // Use the new smart chat system
    body: {
      id,
      metadata: {
        currentPage: typeof window !== 'undefined' ? window.location.pathname : '/dashboard'
      }
    },
    initialMessages,
    experimental_throttle: 10, // Even faster throttling for smart system
    sendExtraMessageFields: true,
    generateId: generateUUID,
    onFinish: () => {
      mutate('/api/history');
    },
    onError: (error) => {
      // Check if this is a usage limit error (429 status)
      if (
        error?.message?.includes('Chat limit exceeded') ||
        error?.message?.includes('429') ||
        (error as { status?: number })?.status === 429
      ) {
        // Redirect to subscription page for usage limit errors
        const message =
          'You have reached your chat limit. Please upgrade your plan to continue.';
        const encodedMessage = encodeURIComponent(message);
        router.push(
          `/dashboard/subscription?limit_exceeded=chat&message=${encodedMessage}`
        );
        return;
      }

      // For other errors, show generic error toast
      toast.error('An error occurred, please try again!');
    },
  });

  const { data: votes } = useSWR<Array<Vote>>(
    `/api/vote?chatId=${id}`,
    fetcher
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>(
    []
  );
  const isArtifactVisible = useArtifactSelector(
    (state) => state.isVisible
  );

  // Check if we're in the initial state (no messages)
  const isInitialState = messages.length === 0;

  // Function to handle form submission
  const onFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit(e);
  };

  return (
    <>
      <div className="flex flex-col flex-1 min-w-0 h-full bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/30 dark:from-[#0B1739] dark:via-[#0B1739]/90 dark:to-[#22304d]/40">
        <ChatHeader
          chatId={id}
          selectedModelId={selectedChatModel}
          selectedVisibilityType={selectedVisibilityType}
          isReadonly={isReadonly}
        />

        {isInitialState ? (
          // Initial state layout - centered content with welcome message and input
          <div className="flex flex-col flex-1 justify-center items-center overflow-y-auto px-4">
            <div className="w-full max-w-4xl">
              <Messages
                chatId={id}
                isLoading={isLoading}
                votes={votes}
                messages={messages}
                setMessages={setMessages}
                reload={reload}
                isReadonly={isReadonly}
                isArtifactVisible={isArtifactVisible}
                append={append}
              />

              {!isReadonly && (
                <div className="mt-6">
                  <form
                    className="flex flex-col mx-auto gap-3"
                    onSubmit={onFormSubmit}
                  >
                    <MultimodalInput
                      chatId={id}
                      input={input}
                      setInput={setInput}
                      handleSubmit={handleSubmit}
                      isLoading={isLoading}
                      stop={stop}
                      attachments={attachments}
                      setAttachments={setAttachments}
                      messages={messages}
                      setMessages={setMessages}
                      append={append}
                      className="bg-white/95 dark:bg-[#22304d]/95 backdrop-blur-xl border-blue-200/50 dark:border-[#0097B1]/30 shadow-lg hover:shadow-xl hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 transition-all duration-300 rounded-2xl"
                    />
                  </form>
                </div>
              )}
            </div>
          </div>
        ) : (
          // Regular layout after messages are sent
          <>
            <div className="flex-1 overflow-y-auto relative">
              <div className="absolute inset-0 px-4 py-2">
                <Messages
                  chatId={id}
                  isLoading={isLoading}
                  votes={votes}
                  messages={messages}
                  setMessages={setMessages}
                  reload={reload}
                  isReadonly={isReadonly}
                  isArtifactVisible={isArtifactVisible}
                  append={append}
                />
              </div>
            </div>

            {!isReadonly && (
              <div className="sticky bottom-0 z-10 w-full bg-gradient-to-t from-white via-white/95 to-transparent dark:from-[#0B1739] dark:via-[#22304d]/95 dark:to-transparent backdrop-blur-md border-t border-blue-200/30 dark:border-[#0097B1]/20">
                <form
                  className="flex mx-auto px-4 md:px-6 pb-4 pt-3 gap-2 w-full max-w-4xl"
                  onSubmit={onFormSubmit}
                >
                  <MultimodalInput
                    chatId={id}
                    input={input}
                    setInput={setInput}
                    handleSubmit={handleSubmit}
                    isLoading={isLoading}
                    stop={stop}
                    attachments={attachments}
                    setAttachments={setAttachments}
                    messages={messages}
                    setMessages={setMessages}
                    append={append}
                    className="bg-white/95 dark:bg-[#22304d]/95 backdrop-blur-xl border-blue-200/50 dark:border-[#0097B1]/30 shadow-lg hover:shadow-xl hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 transition-all duration-300 rounded-2xl"
                  />
                </form>
              </div>
            )}
          </>
        )}
      </div>

      {isArtifactVisible && (
        <div className="border-l border-blue-200/50 dark:border-[#0097B1]/20 h-full bg-gradient-to-br from-blue-50/30 to-cyan-50/30 dark:from-[#0B1739]/80 dark:to-[#22304d]/40 backdrop-blur-sm">
          <Artifact
            chatId={id}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            isLoading={isLoading}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            append={append}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            votes={votes}
            isReadonly={isReadonly}
          />
        </div>
      )}
    </>
  );
});

export const Chat = PureChat;
