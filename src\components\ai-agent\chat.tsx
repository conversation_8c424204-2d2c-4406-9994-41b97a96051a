'use client';

import type { Attachment, Message } from 'ai';
import { useChat } from 'ai/react';
import { useState, memo, useEffect } from 'react';
import useSWR, { useSWRConfig } from 'swr';

import { ChatHeader } from '@/components/ai-agent/chat-header';
import type { Vote } from '@prisma/client';
import { fetcher, generateUUID } from '@/lib/utils';

import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import { VisibilityType } from '@/hooks/use-chat-visibility';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Download,
  FileText,
  Sparkles,
  Zap,
  Brain,
  BarChart3
} from 'lucide-react';
import { UltraSmartDataHandler, useUltraSmartChat } from './ultra-smart-data-handler';

const PureChat = memo(function Chat({
  id,
  initialMessages,
  selectedChatModel,
  selectedVisibilityType,
  isReadonly,
}: {
  id: string;
  initialMessages: Array<Message>;
  selectedChatModel: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
}) {
  const { mutate } = useSWRConfig();
  const router = useRouter();
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    isLoading,
    stop,
    reload,
    data,
  } = useChat({
    id,
    api: '/api/chat/ultra-smart', // Use the new ultra-smart chat system with streaming documents
    body: {
      chatId: id,
      context: {
        page: typeof window !== 'undefined' ? window.location.pathname : '/dashboard',
        userIntent: 'general'
      },
      options: {
        generateDocuments: true,
        includeInsights: true,
        smartMode: true,
        learningEnabled: true
      }
    },
    initialMessages,
    experimental_throttle: 10, // Even faster throttling for ultra-smart system
    sendExtraMessageFields: true,
    generateId: generateUUID,
    onFinish: () => {
      mutate('/api/history');
    },
    onError: (error) => {
      // Check if this is a usage limit error (429 status)
      if (
        error?.message?.includes('Chat limit exceeded') ||
        error?.message?.includes('429') ||
        (error as { status?: number })?.status === 429
      ) {
        // Redirect to subscription page for usage limit errors
        const message =
          'You have reached your chat limit. Please upgrade your plan to continue.';
        const encodedMessage = encodeURIComponent(message);
        router.push(
          `/dashboard/subscription?limit_exceeded=chat&message=${encodedMessage}`
        );
        return;
      }

      // For other errors, show generic error toast
      toast.error('An error occurred, please try again!');
    },
  });

  const { data: votes } = useSWR<Array<Vote>>(
    `/api/vote?chatId=${id}`,
    fetcher
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>(
    []
  );

  // Use the ultra-smart chat hook for enhanced functionality
  const { documents, stats, progress, handlers } = useUltraSmartChat(id);

  const isArtifactVisible = useArtifactSelector(
    (state) => state.isVisible
  );

  // Check if we're in the initial state (no messages)
  const isInitialState = messages.length === 0;

  // Function to handle form submission
  const onFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit(e);
  };

  return (
    <>
      {/* Ultra-Smart Data Handler for streaming responses */}
      <UltraSmartDataHandler
        chatId={id}
        onDocumentGenerated={handlers.onDocumentGenerated}
        onActionExecuted={handlers.onActionExecuted}
        onStatsUpdate={handlers.onStatsUpdate}
        onProgressUpdate={handlers.onProgressUpdate}
      />

      <div className="flex flex-col flex-1 min-w-0 h-full bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/30 dark:from-[#0B1739] dark:via-[#0B1739]/90 dark:to-[#22304d]/40">
        <ChatHeader
          isReadonly={isReadonly}
        />

        {/* Ultra-Smart AI Stats Header */}
        {!isInitialState && (
          <Card className="mx-4 mt-4 mb-2">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Brain className="h-4 w-4 text-blue-500" />
                Ultra-Smart AI Agent
                <Badge variant="secondary" className="ml-auto">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Advanced Mode
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4 text-xs">
                <div className="text-center">
                  <div className="font-semibold text-blue-600">{stats.totalTokens.toLocaleString()}</div>
                  <div className="text-muted-foreground">Tokens</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-green-600">${stats.totalCost.toFixed(4)}</div>
                  <div className="text-muted-foreground">Cost</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-purple-600">{Math.round(stats.avgResponseTime)}ms</div>
                  <div className="text-muted-foreground">Response</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-orange-600">{stats.actionsExecuted}</div>
                  <div className="text-muted-foreground">Actions</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {isInitialState ? (
          // Initial state layout - centered content with welcome message and input
          <div className="flex flex-col flex-1 justify-center items-center overflow-y-auto px-4">
            <div className="w-full max-w-4xl">
              {/* Welcome message for Ultra-Smart AI */}
              <div className="text-center mb-8">
                <div className="flex items-center justify-center gap-2 mb-4">
                  <Brain className="h-8 w-8 text-blue-500" />
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                    Ultra-Smart AI Agent
                  </h1>
                </div>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  I can create invoices with PDFs, generate Excel reports, analyze your data, and perform complex actions with real-time progress updates.
                  Just tell me what you need!
                </p>
                <div className="flex items-center justify-center gap-4 mt-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    Document Generation
                  </div>
                  <div className="flex items-center gap-1">
                    <BarChart3 className="h-4 w-4" />
                    Data Analysis
                  </div>
                  <div className="flex items-center gap-1">
                    <Zap className="h-4 w-4" />
                    Smart Actions
                  </div>
                </div>

                {/* Example prompts */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto">
                  <Button
                    variant="outline"
                    className="text-left h-auto p-4 justify-start"
                    onClick={() => setInput("Create an invoice for Acme Corp for $1,500 with PDF generation")}
                  >
                    <div>
                      <div className="font-medium">Create Invoice with PDF</div>
                      <div className="text-sm text-muted-foreground">Generate professional invoice documents</div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    className="text-left h-auto p-4 justify-start"
                    onClick={() => setInput("Generate a financial report for this quarter with Excel spreadsheet")}
                  >
                    <div>
                      <div className="font-medium">Financial Report</div>
                      <div className="text-sm text-muted-foreground">Create detailed Excel analysis</div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    className="text-left h-auto p-4 justify-start"
                    onClick={() => setInput("Create a vendor analysis spreadsheet with charts")}
                  >
                    <div>
                      <div className="font-medium">Vendor Analysis</div>
                      <div className="text-sm text-muted-foreground">Analyze vendor performance data</div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    className="text-left h-auto p-4 justify-start"
                    onClick={() => setInput("Show me my invoice statistics and create a summary report")}
                  >
                    <div>
                      <div className="font-medium">Invoice Statistics</div>
                      <div className="text-sm text-muted-foreground">Get insights and summaries</div>
                    </div>
                  </Button>
                </div>
              </div>

              <Messages
                chatId={id}
                isLoading={isLoading}
                votes={votes}
                messages={messages}
                setMessages={setMessages}
                reload={reload}
                isReadonly={isReadonly}
                isArtifactVisible={isArtifactVisible}
                append={append}
              />

              {!isReadonly && (
                <div className="mt-6">
                  <form
                    className="flex flex-col mx-auto gap-3"
                    onSubmit={onFormSubmit}
                  >
                    <MultimodalInput
                      chatId={id}
                      input={input}
                      setInput={setInput}
                      handleSubmit={handleSubmit}
                      isLoading={isLoading}
                      stop={stop}
                      attachments={attachments}
                      setAttachments={setAttachments}
                      messages={messages}
                      setMessages={setMessages}
                      append={append}
                      className="bg-white/95 dark:bg-[#22304d]/95 backdrop-blur-xl border-blue-200/50 dark:border-[#0097B1]/30 shadow-lg hover:shadow-xl hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 transition-all duration-300 rounded-2xl"
                    />
                  </form>
                </div>
              )}
            </div>
          </div>
        ) : (
          // Regular layout after messages are sent
          <>
            <div className="flex-1 overflow-y-auto relative">
              <div className="absolute inset-0 px-4 py-2">
                <Messages
                  chatId={id}
                  isLoading={isLoading}
                  votes={votes}
                  messages={messages}
                  setMessages={setMessages}
                  reload={reload}
                  isReadonly={isReadonly}
                  isArtifactVisible={isArtifactVisible}
                  append={append}
                />
              </div>
            </div>

            {/* Generated Documents Section */}
            {documents.length > 0 && (
              <div className="px-4 pb-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-sm">
                      <FileText className="h-4 w-4" />
                      Generated Documents
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {documents.map((doc) => (
                        <Button
                          key={doc.id}
                          variant="outline"
                          className="justify-start"
                          onClick={() => window.open(doc.url, '_blank')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          {doc.filename}
                          <Badge variant="secondary" className="ml-auto">
                            {doc.type.toUpperCase()}
                          </Badge>
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {!isReadonly && (
              <div className="sticky bottom-0 z-10 w-full bg-gradient-to-t from-white via-white/95 to-transparent dark:from-[#0B1739] dark:via-[#22304d]/95 dark:to-transparent backdrop-blur-md border-t border-blue-200/30 dark:border-[#0097B1]/20">
                <form
                  className="flex mx-auto px-4 md:px-6 pb-4 pt-3 gap-2 w-full max-w-4xl"
                  onSubmit={onFormSubmit}
                >
                  <MultimodalInput
                    chatId={id}
                    input={input}
                    setInput={setInput}
                    handleSubmit={handleSubmit}
                    isLoading={isLoading}
                    stop={stop}
                    attachments={attachments}
                    setAttachments={setAttachments}
                    messages={messages}
                    setMessages={setMessages}
                    append={append}
                    className="bg-white/95 dark:bg-[#22304d]/95 backdrop-blur-xl border-blue-200/50 dark:border-[#0097B1]/30 shadow-lg hover:shadow-xl hover:shadow-blue-500/10 dark:hover:shadow-[#0097B1]/10 transition-all duration-300 rounded-2xl"
                  />
                </form>
              </div>
            )}
          </>
        )}
      </div>

      {isArtifactVisible && (
        <div className="border-l border-blue-200/50 dark:border-[#0097B1]/20 h-full bg-gradient-to-br from-blue-50/30 to-cyan-50/30 dark:from-[#0B1739]/80 dark:to-[#22304d]/40 backdrop-blur-sm">
          <Artifact
            chatId={id}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            isLoading={isLoading}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            append={append}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            votes={votes}
            isReadonly={isReadonly}
          />
        </div>
      )}
    </>
  );
});

export const Chat = PureChat;
