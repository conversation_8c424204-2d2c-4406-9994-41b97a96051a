import db from '@/db/db';
import { generateUUID } from '@/lib/utils';
import { DocumentGenerationEngine } from './document-generation-engine';
import { CacheEngine } from './cache-engine';
import { uploadToVercelBlob } from '@/lib/blob';

interface ActionResult {
  success: boolean;
  data?: any;
  message: string;
  type: 'invoice' | 'report' | 'analytics' | 'general' | 'ui' | 'document';
  uiActions?: UIAction[];
  documents?: ActionGeneratedDocument[];
}

interface UIAction {
  type: 'navigate' | 'modal' | 'toast' | 'update' | 'refresh' | 'download';
  target?: string;
  payload?: any;
  delay?: number;
}

interface ActionGeneratedDocument {
  id: string;
  type: 'pdf' | 'excel' | 'image';
  url: string;
  filename: string;
  size: number;
}

interface WorkflowStep {
  id: string;
  action: string;
  parameters: Record<string, any>;
  dependencies?: string[];
  retryCount?: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

/**
 * Advanced Smart Action Engine - Executes complex actions with UI integration
 * Features: Multi-step workflows, document generation, UI actions, and intelligent routing
 */
export class ActionEngine {
  private static workflowCache = new Map<string, WorkflowStep[]>();

  /**
   * Execute action with intelligent workflow management
   */
  static async executeAction(
    intent: string,
    parameters: Record<string, any>,
    userId: string
  ): Promise<ActionResult> {

    try {
      // Check if this is a complex workflow
      if (this.isComplexWorkflow(intent, parameters)) {
        return await this.executeWorkflow(intent, parameters, userId);
      }

      // Execute single action
      switch (intent.toLowerCase()) {
        case 'create_invoice':
          return await this.createInvoiceWithUI(parameters, userId);

        case 'create_invoice_with_pdf':
          return await this.createInvoiceWithPDF(parameters, userId);

        case 'create_spreadsheet':
          return await this.createSpreadsheet(parameters, userId);

        case 'generate_financial_report':
          return await this.generateFinancialReport(parameters, userId);

        case 'list_invoices':
          return await this.listInvoices(parameters, userId);

        case 'get_invoice_stats':
          return await this.getInvoiceStats(userId);

        case 'generate_report':
          return await this.generateReport(parameters, userId);

        case 'analyze_spending':
          return await this.analyzeSpending(parameters, userId);

        case 'generate_document':
          return await this.generateDocument(parameters, userId);

        case 'create_invoice_pdf':
          return await this.createInvoicePDF(parameters, userId);

        case 'export_data':
          return await this.exportData(parameters, userId);

        case 'navigate_to_page':
          return await this.navigateToPage(parameters, userId);

        case 'execute_page_action':
          return await this.executePageAction(parameters, userId);

        case 'bulk_invoice_operations':
          return await this.bulkInvoiceOperations(parameters, userId);

        case 'smart_categorization':
          return await this.smartCategorization(parameters, userId);

        default:
          return {
            success: false,
            message: 'Action not recognized',
            type: 'general'
          };
      }
    } catch (error) {
      console.error('Action Engine Error:', error);
      return {
        success: false,
        message: 'Failed to execute action',
        type: 'general'
      };
    }
  }

  /**
   * Check if this requires a complex workflow
   */
  private static isComplexWorkflow(intent: string, parameters: Record<string, any>): boolean {
    const complexIntents = [
      'create_invoice_with_pdf',
      'generate_financial_report',
      'bulk_invoice_operations',
      'create_spreadsheet'
    ];

    return complexIntents.includes(intent.toLowerCase()) ||
           parameters.workflow === true ||
           parameters.steps?.length > 1;
  }

  /**
   * Execute complex multi-step workflow
   */
  private static async executeWorkflow(
    intent: string,
    parameters: Record<string, any>,
    userId: string
  ): Promise<ActionResult> {
    const workflowId = generateUUID();
    const steps = this.createWorkflowSteps(intent, parameters);

    this.workflowCache.set(workflowId, steps);

    try {
      const results: any[] = [];
      const uiActions: UIAction[] = [];
      const documents: ActionGeneratedDocument[] = [];

      for (const step of steps) {
        step.status = 'running';

        const stepResult = await this.executeAction(step.action, step.parameters, userId);

        if (stepResult.success) {
          step.status = 'completed';
          results.push(stepResult.data);

          if (stepResult.uiActions) {
            uiActions.push(...stepResult.uiActions);
          }

          if (stepResult.documents) {
            documents.push(...stepResult.documents);
          }
        } else {
          step.status = 'failed';
          if (step.retryCount && step.retryCount > 0) {
            step.retryCount--;
            step.status = 'pending';
            // Retry logic could be implemented here
          } else {
            throw new Error(`Workflow step failed: ${step.action}`);
          }
        }
      }

      return {
        success: true,
        message: `Workflow completed successfully with ${steps.length} steps`,
        type: 'general',
        data: { workflowId, results },
        uiActions,
        documents
      };

    } catch (error) {
      return {
        success: false,
        message: `Workflow failed: ${error}`,
        type: 'general'
      };
    }
  }

  /**
   * Create workflow steps based on intent
   */
  private static createWorkflowSteps(intent: string, parameters: Record<string, any>): WorkflowStep[] {
    switch (intent.toLowerCase()) {
      case 'create_invoice_with_pdf':
        return [
          {
            id: generateUUID(),
            action: 'create_invoice',
            parameters: parameters,
            status: 'pending'
          },
          {
            id: generateUUID(),
            action: 'generate_document',
            parameters: { type: 'invoice', format: 'pdf', ...parameters },
            status: 'pending'
          }
        ];

      case 'generate_financial_report':
        return [
          {
            id: generateUUID(),
            action: 'analyze_spending',
            parameters: parameters,
            status: 'pending'
          },
          {
            id: generateUUID(),
            action: 'generate_document',
            parameters: { type: 'report', format: 'all', ...parameters },
            status: 'pending'
          }
        ];

      default:
        return [
          {
            id: generateUUID(),
            action: intent,
            parameters: parameters,
            status: 'pending'
          }
        ];
    }
  }

  /**
   * Create invoice with enhanced UI integration
   */
  private static async createInvoiceWithUI(params: any, userId: string): Promise<ActionResult> {
    const result = await this.createInvoice(params, userId);

    if (result.success) {
      result.uiActions = [
        {
          type: 'toast',
          payload: { message: 'Invoice created successfully!', type: 'success' }
        },
        {
          type: 'navigate',
          target: `/dashboard/invoices/${result.data.id}`
        },
        {
          type: 'refresh',
          target: 'invoice-list'
        }
      ];
    }

    return result;
  }

  /**
   * Create invoice with PDF generation
   */
  private static async createInvoiceWithPDF(params: any, userId: string): Promise<ActionResult> {
    // This will be handled by the workflow system
    return await this.executeWorkflow('create_invoice_with_pdf', params, userId);
  }

  /**
   * Create spreadsheet with financial data
   */
  private static async createSpreadsheet(params: any, userId: string): Promise<ActionResult> {
    try {
      const spreadsheetType = params.type || 'invoice_summary';
      const period = params.period || 'month';

      // Generate spreadsheet data
      const data = await this.buildSpreadsheetData(spreadsheetType, period, userId);

      // Create Excel file using DocumentGenerationEngine
      const generatedDocs = await DocumentGenerationEngine.generateDocument(userId, {
        type: 'analysis',
        format: 'excel',
        data: data,
        options: {
          includeCharts: true,
          includeImages: true,
          includeBranding: true,
          quality: 'high',
          language: 'en',
          rtl: false
        }
      });

      // Convert to ActionGeneratedDocument format
      const documents: ActionGeneratedDocument[] = generatedDocs.map(doc => ({
        id: doc.id,
        type: doc.format as 'pdf' | 'excel' | 'image',
        url: doc.downloadUrl || '',
        filename: `document_${doc.id}.xlsx`,
        size: 0 // Size will be determined after upload
      }));

      return {
        success: true,
        message: `${spreadsheetType} spreadsheet created successfully`,
        type: 'document',
        data: { spreadsheetType, period },
        documents,
        uiActions: [
          {
            type: 'toast',
            payload: { message: 'Spreadsheet generated successfully!', type: 'success' }
          },
          {
            type: 'download',
            payload: { url: documents[0]?.url, filename: documents[0]?.filename }
          }
        ]
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create spreadsheet',
        type: 'document'
      };
    }
  }

  /**
   * Generate comprehensive financial report
   */
  private static async generateFinancialReport(params: any, userId: string): Promise<ActionResult> {
    // This will be handled by the workflow system
    return await this.executeWorkflow('generate_financial_report', params, userId);
  }

  /**
   * Bulk operations on invoices
   */
  private static async bulkInvoiceOperations(params: any, userId: string): Promise<ActionResult> {
    try {
      const operation = params.operation; // 'update_status', 'delete', 'categorize'
      const invoiceIds = params.invoiceIds || [];
      const operationData = params.data || {};

      let successCount = 0;
      const results = [];

      for (const invoiceId of invoiceIds) {
        try {
          switch (operation) {
            case 'update_status':
              await db.invoice.update({
                where: { id: invoiceId, userId },
                data: { status: operationData.status }
              });
              break;

            case 'categorize':
              await db.invoice.update({
                where: { id: invoiceId, userId },
                data: { categoryId: operationData.categoryId }
              });
              break;

            case 'delete':
              await db.invoice.delete({
                where: { id: invoiceId, userId }
              });
              break;
          }

          successCount++;
          results.push({ id: invoiceId, success: true });
        } catch (error) {
          results.push({ id: invoiceId, success: false, error: error });
        }
      }

      return {
        success: true,
        message: `Bulk operation completed: ${successCount}/${invoiceIds.length} invoices processed`,
        type: 'invoice',
        data: { operation, successCount, totalCount: invoiceIds.length, results },
        uiActions: [
          {
            type: 'toast',
            payload: {
              message: `${successCount} invoices ${operation}d successfully`,
              type: 'success'
            }
          },
          {
            type: 'refresh',
            target: 'invoice-list'
          }
        ]
      };
    } catch (error) {
      return {
        success: false,
        message: 'Bulk operation failed',
        type: 'invoice'
      };
    }
  }

  /**
   * Smart categorization using AI
   */
  private static async smartCategorization(params: any, userId: string): Promise<ActionResult> {
    try {
      const invoiceIds = params.invoiceIds || [];
      const categorizedCount = invoiceIds.length; // Simplified for now

      // In a real implementation, this would use AI to categorize invoices
      // based on vendor, amount, description, etc.

      return {
        success: true,
        message: `${categorizedCount} invoices categorized using AI`,
        type: 'invoice',
        data: { categorizedCount },
        uiActions: [
          {
            type: 'toast',
            payload: { message: 'Smart categorization completed!', type: 'success' }
          },
          {
            type: 'refresh',
            target: 'invoice-list'
          }
        ]
      };
    } catch (error) {
      return {
        success: false,
        message: 'Smart categorization failed',
        type: 'invoice'
      };
    }
  }

  /**
   * Build spreadsheet data based on type and period
   */
  private static async buildSpreadsheetData(type: string, period: string, userId: string) {
    const endDate = new Date();
    const startDate = new Date();

    // Calculate date range based on period
    switch (period) {
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(endDate.getMonth() - 1);
    }

    switch (type) {
      case 'invoice_summary':
        return await this.buildInvoiceSummaryData(startDate, endDate, userId);
      case 'vendor_analysis':
        return await this.buildVendorAnalysisData(startDate, endDate, userId);
      case 'expense_breakdown':
        return await this.buildExpenseBreakdownData(startDate, endDate, userId);
      default:
        return await this.buildInvoiceSummaryData(startDate, endDate, userId);
    }
  }

  /**
   * Build invoice summary data for spreadsheet
   */
  private static async buildInvoiceSummaryData(startDate: Date, endDate: Date, userId: string) {
    const invoices = await db.invoice.findMany({
      where: {
        userId,
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        category: true
      },
      orderBy: { createdAt: 'desc' }
    });

    return {
      title: 'Invoice Summary Report',
      headers: ['Invoice Number', 'Vendor', 'Amount', 'Status', 'Category', 'Date', 'Due Date'],
      data: invoices.map(invoice => [
        invoice.invoiceNumber || invoice.id,
        invoice.vendorName || 'Unknown',
        invoice.amount || 0,
        invoice.status,
        invoice.category?.name || 'Uncategorized',
        invoice.issueDate?.toISOString().split('T')[0] || '',
        invoice.dueDate?.toISOString().split('T')[0] || ''
      ]),
      summary: {
        totalInvoices: invoices.length,
        totalAmount: invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0),
        paidAmount: invoices.filter(inv => inv.status === 'PAID').reduce((sum, inv) => sum + (inv.amount || 0), 0),
        pendingAmount: invoices.filter(inv => inv.status === 'PENDING').reduce((sum, inv) => sum + (inv.amount || 0), 0)
      }
    };
  }

  /**
   * Build vendor analysis data for spreadsheet
   */
  private static async buildVendorAnalysisData(startDate: Date, endDate: Date, userId: string) {
    const vendorStats = await db.invoice.groupBy({
      by: ['vendorName'],
      where: {
        userId,
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        vendorName: { not: null }
      },
      _sum: { amount: true },
      _count: { id: true },
      _avg: { amount: true },
      orderBy: { _sum: { amount: 'desc' } }
    });

    return {
      title: 'Vendor Analysis Report',
      headers: ['Vendor Name', 'Total Amount', 'Invoice Count', 'Average Amount', 'Percentage of Total'],
      data: vendorStats.map(vendor => {
        const totalAmount = vendor._sum.amount || 0;
        const totalSum = vendorStats.reduce((sum, v) => sum + (v._sum.amount || 0), 0);
        const percentage = totalSum > 0 ? ((totalAmount / totalSum) * 100).toFixed(2) : '0';

        return [
          vendor.vendorName,
          totalAmount,
          vendor._count.id,
          vendor._avg.amount || 0,
          `${percentage}%`
        ];
      }),
      summary: {
        totalVendors: vendorStats.length,
        totalAmount: vendorStats.reduce((sum, v) => sum + (v._sum.amount || 0), 0)
      }
    };
  }

  /**
   * Build expense breakdown data for spreadsheet
   */
  private static async buildExpenseBreakdownData(startDate: Date, endDate: Date, userId: string) {
    const categoryStats = await db.invoice.groupBy({
      by: ['categoryId'],
      where: {
        userId,
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      _sum: { amount: true },
      _count: { id: true }
    });

    // Get category names
    const categoryIds = categoryStats.map(stat => stat.categoryId).filter((id): id is string => Boolean(id));
    const categories = await db.category.findMany({
      where: { id: { in: categoryIds } }
    });

    const categoryMap = new Map(categories.map(cat => [cat.id, cat.name]));

    return {
      title: 'Expense Breakdown Report',
      headers: ['Category', 'Total Amount', 'Invoice Count', 'Percentage of Total'],
      data: categoryStats.map(stat => {
        const totalAmount = stat._sum.amount || 0;
        const totalSum = categoryStats.reduce((sum, s) => sum + (s._sum.amount || 0), 0);
        const percentage = totalSum > 0 ? ((totalAmount / totalSum) * 100).toFixed(2) : '0';

        return [
          categoryMap.get(stat.categoryId || '') || 'Uncategorized',
          totalAmount,
          stat._count.id,
          `${percentage}%`
        ];
      }),
      summary: {
        totalCategories: categoryStats.length,
        totalAmount: categoryStats.reduce((sum, s) => sum + (s._sum.amount || 0), 0)
      }
    };
  }

  private static async createInvoice(params: any, userId: string): Promise<ActionResult> {
    const invoice = await db.invoice.create({
      data: {
        id: generateUUID(),
        invoiceNumber: params.invoiceNumber || `INV-${Date.now()}`,
        vendorName: params.vendorName,
        amount: parseFloat(params.amount) || 0,
        currency: params.currency || 'USD',
        status: 'PENDING',
        issueDate: params.issueDate ? new Date(params.issueDate) : new Date(),
        dueDate: params.dueDate ? new Date(params.dueDate) : null,
        notes: params.notes,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    });

    return {
      success: true,
      data: invoice,
      message: `Invoice ${invoice.invoiceNumber} created successfully`,
      type: 'invoice'
    };
  }

  private static async listInvoices(params: any, userId: string): Promise<ActionResult> {
    const limit = Math.min(params.limit || 10, 50);
    const status = params.status;
    
    const invoices = await db.invoice.findMany({
      where: {
        userId,
        ...(status && { status: status.toUpperCase() })
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        invoiceNumber: true,
        vendorName: true,
        amount: true,
        currency: true,
        status: true,
        issueDate: true,
        dueDate: true,
        createdAt: true,
      }
    });

    return {
      success: true,
      data: invoices,
      message: `Found ${invoices.length} invoices`,
      type: 'invoice'
    };
  }

  private static async getInvoiceStats(userId: string): Promise<ActionResult> {
    const [total, pending, paid, overdue] = await Promise.all([
      db.invoice.aggregate({
        where: { userId },
        _count: { id: true },
        _sum: { amount: true }
      }),
      db.invoice.aggregate({
        where: { userId, status: 'PENDING' },
        _count: { id: true },
        _sum: { amount: true }
      }),
      db.invoice.aggregate({
        where: { userId, status: 'PAID' },
        _count: { id: true },
        _sum: { amount: true }
      }),
      db.invoice.aggregate({
        where: { userId, status: 'OVERDUE' },
        _count: { id: true },
        _sum: { amount: true }
      })
    ]);

    const stats = {
      total: { count: total._count.id, amount: total._sum.amount || 0 },
      pending: { count: pending._count.id, amount: pending._sum.amount || 0 },
      paid: { count: paid._count.id, amount: paid._sum.amount || 0 },
      overdue: { count: overdue._count.id, amount: overdue._sum.amount || 0 }
    };

    return {
      success: true,
      data: stats,
      message: 'Invoice statistics retrieved',
      type: 'analytics'
    };
  }

  private static async generateReport(params: any, userId: string): Promise<ActionResult> {
    const reportType = params.type || 'summary';
    const period = params.period || 'month';
    
    // Generate report based on type
    const reportData = await this.buildReportData(reportType, period, userId);
    
    const report = await db.report.create({
      data: {
        id: generateUUID(),
        title: `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`,
        reportType: reportType.toUpperCase(),
        period,
        status: 'COMPLETED',
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    });

    return {
      success: true,
      data: { report, data: reportData },
      message: `${reportType} report generated successfully`,
      type: 'report'
    };
  }

  private static async analyzeSpending(params: any, userId: string): Promise<ActionResult> {
    const period = params.period || 30; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - period);

    const [vendorSpending, categorySpending, trends] = await Promise.all([
      // Vendor spending analysis
      db.invoice.groupBy({
        by: ['vendorName'],
        where: {
          userId,
          createdAt: { gte: startDate },
          vendorName: { not: null }
        },
        _sum: { amount: true },
        _count: { id: true },
        orderBy: { _sum: { amount: 'desc' } },
        take: 10
      }),
      
      // Category spending (if categories exist)
      db.invoice.groupBy({
        by: ['categoryId'],
        where: {
          userId,
          createdAt: { gte: startDate },
          categoryId: { not: null }
        },
        _sum: { amount: true },
        orderBy: { _sum: { amount: 'desc' } }
      }),
      
      // Daily spending trends
      db.$queryRaw`
        SELECT DATE(createdAt) as date, SUM(amount) as total
        FROM Invoice 
        WHERE userId = ${userId} AND createdAt >= ${startDate}
        GROUP BY DATE(createdAt)
        ORDER BY date DESC
        LIMIT 30
      `
    ]);

    const analysis = {
      topVendors: vendorSpending.map(v => ({
        vendor: v.vendorName,
        amount: v._sum.amount,
        invoiceCount: v._count.id
      })),
      categoryBreakdown: categorySpending,
      dailyTrends: trends,
      period: `${period} days`,
      totalSpent: vendorSpending.reduce((sum, v) => sum + (v._sum.amount || 0), 0)
    };

    return {
      success: true,
      data: analysis,
      message: `Spending analysis for the last ${period} days`,
      type: 'analytics'
    };
  }

  private static async buildReportData(type: string, period: string, userId: string) {
    // Simplified report data building
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    const invoices = await db.invoice.findMany({
      where: {
        userId,
        createdAt: { gte: startDate, lte: endDate }
      }
    });

    return {
      period: `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`,
      totalInvoices: invoices.length,
      totalAmount: invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0),
      averageAmount: invoices.length > 0 ? invoices.reduce((sum, inv) => sum + (inv.amount || 0), 0) / invoices.length : 0,
      statusBreakdown: {
        pending: invoices.filter(i => i.status === 'PENDING').length,
        paid: invoices.filter(i => i.status === 'PAID').length,
        overdue: invoices.filter(i => i.status === 'OVERDUE').length,
      }
    };
  }

  /**
   * Generate documents (PDF, Excel, etc.)
   */
  private static async generateDocument(params: any, userId: string): Promise<ActionResult> {
    try {
      const documentType = params.type || 'report';
      const format = params.format || 'pdf';

      return {
        success: true,
        message: `${documentType} ${format} generation requested`,
        type: 'general',
        data: {
          documentType,
          format,
          action: 'generate_document',
          parameters: params
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to generate document',
        type: 'general'
      };
    }
  }

  /**
   * Create invoice PDF
   */
  private static async createInvoicePDF(params: any, userId: string): Promise<ActionResult> {
    try {
      const invoiceId = params.invoiceId;

      if (invoiceId) {
        const invoice = await db.invoice.findUnique({
          where: { id: invoiceId, userId }
        });
        if (!invoice) {
          return {
            success: false,
            message: 'Invoice not found',
            type: 'invoice'
          };
        }
      }

      return {
        success: true,
        message: 'Invoice PDF creation requested',
        type: 'invoice',
        data: {
          action: 'create_invoice_pdf',
          invoiceId,
          template: params.template || 'modern'
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to create invoice PDF',
        type: 'invoice'
      };
    }
  }

  /**
   * Export data to Excel
   */
  private static async exportData(params: any, userId: string): Promise<ActionResult> {
    try {
      const dataType = params.dataType || 'invoices';

      return {
        success: true,
        message: `${dataType} export requested`,
        type: 'general',
        data: {
          action: 'export_data',
          dataType,
          format: 'excel'
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to export data',
        type: 'general'
      };
    }
  }

  /**
   * Navigate to a specific page
   */
  private static async navigateToPage(params: any, userId: string): Promise<ActionResult> {
    const targetPage = params.page || params.target;

    if (!targetPage) {
      return {
        success: false,
        message: 'No target page specified',
        type: 'general'
      };
    }

    return {
      success: true,
      message: `Navigate to ${targetPage}`,
      type: 'general',
      data: {
        action: 'navigate',
        page: targetPage
      }
    };
  }

  /**
   * Execute a page-specific action
   */
  private static async executePageAction(params: any, userId: string): Promise<ActionResult> {
    try {
      return {
        success: true,
        message: 'Page action execution requested',
        type: 'general',
        data: {
          action: 'execute_page_action',
          page: params.page || '/dashboard',
          actionType: params.actionType,
          parameters: params.parameters || {}
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to execute page action',
        type: 'general'
      };
    }
  }
}

/**
 * Intent detection from AI responses
 */
export function detectIntent(message: string): { intent: string; confidence: number; parameters: Record<string, any> } {
  const lowerMessage = message.toLowerCase();
  
  // Enhanced intent detection patterns with new capabilities
  const patterns = [
    { intent: 'create_invoice', keywords: ['create', 'new', 'add', 'invoice'], confidence: 0.8 },
    { intent: 'list_invoices', keywords: ['list', 'show', 'display', 'invoices'], confidence: 0.8 },
    { intent: 'get_invoice_stats', keywords: ['stats', 'statistics', 'summary', 'overview'], confidence: 0.7 },
    { intent: 'generate_report', keywords: ['report', 'generate', 'create report'], confidence: 0.8 },
    { intent: 'analyze_spending', keywords: ['analyze', 'spending', 'analysis', 'breakdown'], confidence: 0.7 },
    { intent: 'generate_document', keywords: ['document', 'pdf', 'excel', 'generate document'], confidence: 0.8 },
    { intent: 'create_invoice_pdf', keywords: ['invoice pdf', 'pdf invoice', 'download invoice'], confidence: 0.8 },
    { intent: 'export_data', keywords: ['export', 'download', 'excel export', 'csv'], confidence: 0.8 },
    { intent: 'navigate_to_page', keywords: ['go to', 'navigate', 'open', 'show me'], confidence: 0.7 },
    { intent: 'execute_page_action', keywords: ['click', 'submit', 'execute', 'run'], confidence: 0.6 },
  ];
  
  for (const pattern of patterns) {
    const matches = pattern.keywords.filter(keyword => lowerMessage.includes(keyword));
    if (matches.length > 0) {
      return {
        intent: pattern.intent,
        confidence: pattern.confidence * (matches.length / pattern.keywords.length),
        parameters: extractParameters(message, pattern.intent)
      };
    }
  }
  
  return { intent: 'general', confidence: 0.1, parameters: {} };
}

function extractParameters(message: string, intent: string): Record<string, any> {
  // Simple parameter extraction based on intent
  const params: Record<string, any> = {};
  
  // Extract common parameters
  const amountMatch = message.match(/\$?(\d+(?:\.\d{2})?)/);
  if (amountMatch) params.amount = amountMatch[1];
  
  const vendorMatch = message.match(/(?:vendor|company|from)\s+([A-Za-z\s]+)/i);
  if (vendorMatch) params.vendorName = vendorMatch[1].trim();
  
  const statusMatch = message.match(/(?:status|state)\s+(pending|paid|overdue)/i);
  if (statusMatch) params.status = statusMatch[1];
  
  return params;
}
