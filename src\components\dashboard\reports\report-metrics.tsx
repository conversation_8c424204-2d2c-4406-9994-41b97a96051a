'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ArrowUpRight,
  ArrowDownRight,
  FileText,
  Clock,
  Calendar,
  Users,
  Loader2,
} from 'lucide-react';
import { Area, AreaChart, ResponsiveContainer } from 'recharts';
import { getReportMetrics } from '@/lib/actions/reports';
import Colors from '../../theme/Colors';

interface ReportMetricsData {
  totalReports: number;
  scheduledReports: number;
  customReports: number;
  generatedThisMonth: number;
  monthlyTrends: Array<{ month: string; reports: number }>;
  totalReportsChange: number;
  scheduledReportsChange: number;
  customReportsChange: number;
  generatedThisMonthChange: number;
}

export function ReportMetrics() {
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<ReportMetricsData>({
    totalReports: 0,
    scheduledReports: 0,
    customReports: 0,
    generatedThisMonth: 0,
    monthlyTrends: [],
    totalReportsChange: 0,
    scheduledReportsChange: 0,
    customReportsChange: 0,
    generatedThisMonthChange: 0,
  });

  useEffect(() => {
    async function fetchMetrics() {
      try {
        const data = await getReportMetrics();
        setMetrics(data);
      } finally {
        setLoading(false);
      }
    }

    fetchMetrics();
  }, []);
  if (loading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="border-0 shadow-lg bg-gradient-to-br from-background to-muted/30 dark:from-background dark:to-muted/10">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Loading metrics...
              </CardTitle>
              <div className="h-4 w-4 rounded bg-muted animate-pulse" />
            </CardHeader>
            <CardContent className="flex justify-center items-center py-8">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                <div className="h-2 w-16 bg-muted rounded animate-pulse" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-muted/30 dark:from-background dark:to-muted/10 hover:shadow-xl transition-all duration-300 group">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total Reports
          </CardTitle>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 group-hover:scale-110 transition-transform duration-200">
            <FileText className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            {metrics.totalReports.toLocaleString()}
          </div>
          <div className="flex items-center text-xs mt-2">
            {metrics.totalReportsChange >= 0 ? (
              <>
                <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                <span className="text-emerald-600 font-medium">
                  +{metrics.totalReportsChange.toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                <span className="text-red-600 font-medium">
                  {metrics.totalReportsChange.toFixed(1)}%
                </span>
              </>
            )}
            <span className="text-muted-foreground ml-1">vs last month</span>
          </div>
          <div className="mt-4 h-[50px] report-metric-chart">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={metrics.monthlyTrends}
                margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
              >
                <Area
                  type="monotone"
                  dataKey="reports"
                  stroke="#3b82f6"
                  fill="url(#blueGradient)"
                  strokeWidth={2}
                />
                <defs>
                  <linearGradient id="blueGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-muted/30 dark:from-background dark:to-muted/10 hover:shadow-xl transition-all duration-300 group">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Scheduled Reports
          </CardTitle>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-green-500 to-green-600 group-hover:scale-110 transition-transform duration-200">
            <Calendar className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
            {metrics.scheduledReports.toLocaleString()}
          </div>
          <div className="flex items-center text-xs mt-2">
            {metrics.scheduledReportsChange >= 0 ? (
              <>
                <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                <span className="text-emerald-600 font-medium">
                  +{metrics.scheduledReportsChange.toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                <span className="text-red-600 font-medium">
                  {metrics.scheduledReportsChange.toFixed(1)}%
                </span>
              </>
            )}
            <span className="text-muted-foreground ml-1">vs last month</span>
          </div>
          <div className="mt-4 h-[50px] report-metric-chart">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={metrics.monthlyTrends}
                margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
              >
                <Area
                  type="monotone"
                  dataKey="reports"
                  stroke="#10b981"
                  fill="url(#greenGradient)"
                  strokeWidth={2}
                />
                <defs>
                  <linearGradient id="greenGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-muted/30 dark:from-background dark:to-muted/10 hover:shadow-xl transition-all duration-300 group">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Custom Reports
          </CardTitle>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 group-hover:scale-110 transition-transform duration-200">
            <Users className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
            {metrics.customReports.toLocaleString()}
          </div>
          <div className="flex items-center text-xs mt-2">
            {metrics.customReportsChange >= 0 ? (
              <>
                <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                <span className="text-emerald-600 font-medium">
                  +{metrics.customReportsChange.toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                <span className="text-red-600 font-medium">
                  {metrics.customReportsChange.toFixed(1)}%
                </span>
              </>
            )}
            <span className="text-muted-foreground ml-1">vs last month</span>
          </div>
          <div className="mt-4 h-[50px] report-metric-chart">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={metrics.monthlyTrends}
                margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
              >
                <Area
                  type="monotone"
                  dataKey="reports"
                  stroke="#f59e0b"
                  fill="url(#orangeGradient)"
                  strokeWidth={2}
                />
                <defs>
                  <linearGradient id="orangeGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-lg bg-gradient-to-br from-background to-muted/30 dark:from-background dark:to-muted/10 hover:shadow-xl transition-all duration-300 group">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Generated This Month
          </CardTitle>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 group-hover:scale-110 transition-transform duration-200">
            <Clock className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            {metrics.generatedThisMonth.toLocaleString()}
          </div>
          <div className="flex items-center text-xs mt-2">
            {metrics.generatedThisMonthChange >= 0 ? (
              <>
                <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                <span className="text-emerald-600 font-medium">
                  +{metrics.generatedThisMonthChange.toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                <span className="text-red-600 font-medium">
                  {metrics.generatedThisMonthChange.toFixed(1)}%
                </span>
              </>
            )}
            <span className="text-muted-foreground ml-1">vs last month</span>
          </div>
          <div className="mt-4 h-[50px] report-metric-chart">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={metrics.monthlyTrends}
                margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
              >
                <Area
                  type="monotone"
                  dataKey="reports"
                  stroke="#8b5cf6"
                  fill="url(#purpleGradient)"
                  strokeWidth={2}
                />
                <defs>
                  <linearGradient id="purpleGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
