'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ArrowUpRight,
  ArrowDownRight,
  FileText,
  Clock,
  Calendar,
  Users,
  Loader2,
} from 'lucide-react';
import { Area, AreaChart, ResponsiveContainer } from 'recharts';
import { getReportMetrics } from '@/lib/actions/reports';
import Colors from '../../theme/Colors';

interface ReportMetricsData {
  totalReports: number;
  scheduledReports: number;
  customReports: number;
  generatedThisMonth: number;
  monthlyTrends: Array<{ month: string; reports: number }>;
  totalReportsChange: number;
  scheduledReportsChange: number;
  customReportsChange: number;
  generatedThisMonthChange: number;
}

export function ReportMetrics() {
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<ReportMetricsData>({
    totalReports: 0,
    scheduledReports: 0,
    customReports: 0,
    generatedThisMonth: 0,
    monthlyTrends: [],
    totalReportsChange: 0,
    scheduledReportsChange: 0,
    customReportsChange: 0,
    generatedThisMonthChange: 0,
  });

  useEffect(() => {
    async function fetchMetrics() {
      try {
        const data = await getReportMetrics();
        setMetrics(data);
      } finally {
        setLoading(false);
      }
    }

    fetchMetrics();
  }, []);
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Loading...
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Total Reports
          </CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {metrics.totalReports}
          </div>
          <div className="flex items-center text-xs text-muted-foreground">
            {metrics.totalReportsChange >= 0 ? (
              <>
                <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                <span className="text-emerald-500">
                  +{metrics.totalReportsChange.toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                <span className="text-red-500">
                  {metrics.totalReportsChange.toFixed(1)}%
                </span>
              </>
            )}
            {' from last month'}
          </div>
          <div className="mt-4 h-[40px] report-metric-chart">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={metrics.monthlyTrends}
                margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
              >
                <Area
                  type="monotone"
                  dataKey="reports"
                  stroke={Colors.success}
                  fill={Colors.success + '20'}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Scheduled Reports
          </CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {metrics.scheduledReports}
          </div>
          <div className="flex items-center text-xs text-muted-foreground">
            {metrics.scheduledReportsChange >= 0 ? (
              <>
                <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                <span className="text-emerald-500">
                  +{metrics.scheduledReportsChange.toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                <span className="text-red-500">
                  {metrics.scheduledReportsChange.toFixed(1)}%
                </span>
              </>
            )}
            {' from last month'}
          </div>
          <div className="mt-4 h-[40px] report-metric-chart">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={metrics.monthlyTrends}
                margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
              >
                <Area
                  type="monotone"
                  dataKey="reports"
                  stroke={Colors.primary}
                  fill={Colors.primary + '20'}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Custom Reports
          </CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {metrics.customReports}
          </div>
          <div className="flex items-center text-xs text-muted-foreground">
            {metrics.customReportsChange >= 0 ? (
              <>
                <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                <span className="text-emerald-500">
                  +{metrics.customReportsChange.toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                <span className="text-red-500">
                  {metrics.customReportsChange.toFixed(1)}%
                </span>
              </>
            )}
            {' from last month'}
          </div>
          <div className="mt-4 h-[40px] report-metric-chart">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={metrics.monthlyTrends}
                margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
              >
                <Area
                  type="monotone"
                  dataKey="reports"
                  stroke={Colors.warning}
                  fill={Colors.warning + '20'}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Generated This Month
          </CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {metrics.generatedThisMonth}
          </div>
          <div className="flex items-center text-xs text-muted-foreground">
            {metrics.generatedThisMonthChange >= 0 ? (
              <>
                <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                <span className="text-emerald-500">
                  +{metrics.generatedThisMonthChange.toFixed(1)}%
                </span>
              </>
            ) : (
              <>
                <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                <span className="text-red-500">
                  {metrics.generatedThisMonthChange.toFixed(1)}%
                </span>
              </>
            )}
            {' from last month'}
          </div>
          <div className="mt-4 h-[40px] report-metric-chart">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={metrics.monthlyTrends}
                margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
              >
                <Area
                  type="monotone"
                  dataKey="reports"
                  stroke={Colors.error}
                  fill={Colors.error + '20'}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
