import * as XLSX from 'xlsx';
import { CacheEngine } from '../ai/cache-engine';

interface ExcelData {
  sheets: ExcelSheet[];
  metadata?: {
    title: string;
    author: string;
    created: Date;
  };
}

interface ExcelSheet {
  name: string;
  data: any[][];
  headers?: string[];
  formatting?: CellFormatting[];
  charts?: ChartDefinition[];
}

interface CellFormatting {
  range: string;
  style: {
    font?: { bold?: boolean; color?: string; size?: number };
    fill?: { fgColor?: string };
    border?: boolean;
    alignment?: { horizontal?: string; vertical?: string };
  };
}

interface ChartDefinition {
  type: 'bar' | 'line' | 'pie' | 'scatter';
  title: string;
  dataRange: string;
  position: { row: number; col: number };
}

interface FinancialDashboardData {
  summary: {
    totalRevenue: number;
    totalExpenses: number;
    netProfit: number;
    invoiceCount: number;
  };
  monthlyData: Array<{
    month: string;
    revenue: number;
    expenses: number;
    profit: number;
  }>;
  topVendors: Array<{
    name: string;
    amount: number;
    invoiceCount: number;
  }>;
  categoryBreakdown: Array<{
    category: string;
    amount: number;
    percentage: number;
  }>;
}

/**
 * Advanced Excel Generation Engine
 * Creates professional Excel files with charts, formatting, and multiple sheets
 */
export class ExcelGenerator {
  
  /**
   * Generate financial dashboard Excel file
   */
  static async generateFinancialDashboard(data: FinancialDashboardData): Promise<Buffer> {
    const cacheKey = CacheEngine.generateKey('excel-dashboard', JSON.stringify(data).slice(0, 100));
    
    return CacheEngine.getOrSet(
      cacheKey,
      'action-results',
      async () => {
        const workbook = XLSX.utils.book_new();

        // Summary Sheet
        const summarySheet = this.createSummarySheet(data.summary);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

        // Monthly Trends Sheet
        const trendsSheet = this.createTrendsSheet(data.monthlyData);
        XLSX.utils.book_append_sheet(workbook, trendsSheet, 'Monthly Trends');

        // Vendor Analysis Sheet
        const vendorSheet = this.createVendorSheet(data.topVendors);
        XLSX.utils.book_append_sheet(workbook, vendorSheet, 'Top Vendors');

        // Category Breakdown Sheet
        const categorySheet = this.createCategorySheet(data.categoryBreakdown);
        XLSX.utils.book_append_sheet(workbook, categorySheet, 'Categories');

        // Charts & Analysis Sheet
        const chartsSheet = this.createChartsSheet(data);
        XLSX.utils.book_append_sheet(workbook, chartsSheet, 'Charts');

        return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      }
    );
  }

  /**
   * Generate data export Excel file
   */
  static async generateDataExport(
    data: any[],
    options: {
      sheetName?: string;
      includeHeaders?: boolean;
      formatting?: 'basic' | 'professional' | 'minimal';
      filters?: boolean;
    } = {}
  ): Promise<Buffer> {
    const workbook = XLSX.utils.book_new();
    
    // Convert data to worksheet
    const worksheet = XLSX.utils.json_to_sheet(data, {
      header: options.includeHeaders !== false ? undefined : []
    });

    // Apply formatting based on style
    if (options.formatting === 'professional') {
      this.applyProfessionalFormatting(worksheet, data);
    } else if (options.formatting === 'basic') {
      this.applyBasicFormatting(worksheet);
    }

    // Add filters if requested
    if (options.filters && data.length > 0) {
      const range = XLSX.utils.encode_range({
        s: { c: 0, r: 0 },
        e: { c: Object.keys(data[0]).length - 1, r: data.length }
      });
      worksheet['!autofilter'] = { ref: range };
    }

    XLSX.utils.book_append_sheet(workbook, worksheet, options.sheetName || 'Data');
    
    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  /**
   * Generate analytics workbook with multiple sheets and charts
   */
  static async generateAnalyticsWorkbook(
    analytics: {
      overview: any;
      trends: any[];
      comparisons: any[];
      forecasts: any[];
    }
  ): Promise<Buffer> {
    const workbook = XLSX.utils.book_new();

    // Overview Sheet
    const overviewData = this.flattenObject(analytics.overview);
    const overviewSheet = XLSX.utils.json_to_sheet([overviewData]);
    this.applyHeaderFormatting(overviewSheet);
    XLSX.utils.book_append_sheet(workbook, overviewSheet, 'Overview');

    // Trends Sheet
    if (analytics.trends.length > 0) {
      const trendsSheet = XLSX.utils.json_to_sheet(analytics.trends);
      this.applyProfessionalFormatting(trendsSheet, analytics.trends);
      XLSX.utils.book_append_sheet(workbook, trendsSheet, 'Trends');
    }

    // Comparisons Sheet
    if (analytics.comparisons.length > 0) {
      const comparisonsSheet = XLSX.utils.json_to_sheet(analytics.comparisons);
      this.applyBasicFormatting(comparisonsSheet);
      XLSX.utils.book_append_sheet(workbook, comparisonsSheet, 'Comparisons');
    }

    // Forecasts Sheet
    if (analytics.forecasts.length > 0) {
      const forecastsSheet = XLSX.utils.json_to_sheet(analytics.forecasts);
      this.applyForecastFormatting(forecastsSheet);
      XLSX.utils.book_append_sheet(workbook, forecastsSheet, 'Forecasts');
    }

    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  /**
   * Generate custom Excel file from structured data
   */
  static async generateCustomExcel(excelData: ExcelData): Promise<Buffer> {
    const workbook = XLSX.utils.book_new();

    // Set workbook metadata
    if (excelData.metadata) {
      workbook.Props = {
        Title: excelData.metadata.title,
        Author: excelData.metadata.author,
        CreatedDate: excelData.metadata.created
      };
    }

    // Create each sheet
    excelData.sheets.forEach(sheetData => {
      let worksheet: XLSX.WorkSheet;

      if (sheetData.headers && sheetData.data) {
        // Create sheet with headers
        const fullData = [sheetData.headers, ...sheetData.data];
        worksheet = XLSX.utils.aoa_to_sheet(fullData);
      } else {
        // Create sheet from data only
        worksheet = XLSX.utils.aoa_to_sheet(sheetData.data);
      }

      // Apply formatting if specified
      if (sheetData.formatting) {
        this.applyCustomFormatting(worksheet, sheetData.formatting);
      }

      XLSX.utils.book_append_sheet(workbook, worksheet, sheetData.name);
    });

    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  // Private helper methods
  private static createSummarySheet(summary: FinancialDashboardData['summary']): XLSX.WorkSheet {
    const data = [
      ['Financial Summary', ''],
      ['', ''],
      ['Metric', 'Value'],
      ['Total Revenue', `$${summary.totalRevenue.toLocaleString()}`],
      ['Total Expenses', `$${summary.totalExpenses.toLocaleString()}`],
      ['Net Profit', `$${summary.netProfit.toLocaleString()}`],
      ['Invoice Count', summary.invoiceCount.toString()],
      ['', ''],
      ['Profit Margin', `${((summary.netProfit / summary.totalRevenue) * 100).toFixed(1)}%`],
      ['Avg Invoice Value', `$${(summary.totalRevenue / summary.invoiceCount).toFixed(2)}`]
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // Apply formatting
    worksheet['A1'].s = { font: { bold: true, size: 16 }, alignment: { horizontal: 'center' } };
    worksheet['A3'].s = { font: { bold: true }, fill: { fgColor: { rgb: 'E3F2FD' } } };
    worksheet['B3'].s = { font: { bold: true }, fill: { fgColor: { rgb: 'E3F2FD' } } };

    return worksheet;
  }

  private static createTrendsSheet(monthlyData: FinancialDashboardData['monthlyData']): XLSX.WorkSheet {
    const headers = ['Month', 'Revenue', 'Expenses', 'Profit', 'Profit Margin %'];
    const data = monthlyData.map(item => [
      item.month,
      item.revenue,
      item.expenses,
      item.profit,
      ((item.profit / item.revenue) * 100).toFixed(1)
    ]);

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
    this.applyHeaderFormatting(worksheet);
    
    return worksheet;
  }

  private static createVendorSheet(vendors: FinancialDashboardData['topVendors']): XLSX.WorkSheet {
    const headers = ['Vendor Name', 'Total Amount', 'Invoice Count', 'Avg Invoice Value'];
    const data = vendors.map(vendor => [
      vendor.name,
      vendor.amount,
      vendor.invoiceCount,
      (vendor.amount / vendor.invoiceCount).toFixed(2)
    ]);

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
    this.applyHeaderFormatting(worksheet);
    
    return worksheet;
  }

  private static createCategorySheet(categories: FinancialDashboardData['categoryBreakdown']): XLSX.WorkSheet {
    const headers = ['Category', 'Amount', 'Percentage'];
    const data = categories.map(cat => [
      cat.category,
      cat.amount,
      `${cat.percentage.toFixed(1)}%`
    ]);

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
    this.applyHeaderFormatting(worksheet);
    
    return worksheet;
  }

  private static createChartsSheet(data: FinancialDashboardData): XLSX.WorkSheet {
    const chartData = [
      ['Chart Descriptions', ''],
      ['', ''],
      ['Monthly Revenue Trend', 'Line chart showing revenue over time'],
      ['Vendor Distribution', 'Pie chart of top vendors by amount'],
      ['Category Breakdown', 'Bar chart of expenses by category'],
      ['Profit Margin Trend', 'Line chart showing profit margin changes'],
      ['', ''],
      ['Note: Charts can be created using Excel\'s built-in chart tools'],
      ['Data for charts is available in other sheets']
    ];

    return XLSX.utils.aoa_to_sheet(chartData);
  }

  private static applyProfessionalFormatting(worksheet: XLSX.WorkSheet, data: any[]): void {
    if (data.length === 0) return;

    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    
    // Header formatting
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
      if (worksheet[cellAddress]) {
        worksheet[cellAddress].s = {
          font: { bold: true, color: { rgb: 'FFFFFF' } },
          fill: { fgColor: { rgb: '1976D2' } },
          alignment: { horizontal: 'center' }
        };
      }
    }

    // Alternating row colors
    for (let row = 1; row <= range.e.r; row++) {
      if (row % 2 === 0) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (worksheet[cellAddress]) {
            worksheet[cellAddress].s = {
              fill: { fgColor: { rgb: 'F5F5F5' } }
            };
          }
        }
      }
    }
  }

  private static applyBasicFormatting(worksheet: XLSX.WorkSheet): void {
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    
    // Header formatting only
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
      if (worksheet[cellAddress]) {
        worksheet[cellAddress].s = {
          font: { bold: true },
          fill: { fgColor: { rgb: 'E0E0E0' } }
        };
      }
    }
  }

  private static applyHeaderFormatting(worksheet: XLSX.WorkSheet): void {
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
      if (worksheet[cellAddress]) {
        worksheet[cellAddress].s = {
          font: { bold: true, color: { rgb: 'FFFFFF' } },
          fill: { fgColor: { rgb: '2196F3' } },
          alignment: { horizontal: 'center' }
        };
      }
    }
  }

  private static applyForecastFormatting(worksheet: XLSX.WorkSheet): void {
    this.applyHeaderFormatting(worksheet);
    
    // Add special formatting for forecast data (e.g., different colors for projected vs actual)
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    
    for (let row = 1; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        if (worksheet[cellAddress]) {
          worksheet[cellAddress].s = {
            fill: { fgColor: { rgb: 'FFF3E0' } }, // Light orange for forecasts
            font: { italic: true }
          };
        }
      }
    }
  }

  private static applyCustomFormatting(worksheet: XLSX.WorkSheet, formatting: CellFormatting[]): void {
    formatting.forEach(format => {
      const range = XLSX.utils.decode_range(format.range);
      
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (worksheet[cellAddress]) {
            worksheet[cellAddress].s = format.style;
          }
        }
      }
    });
  }

  private static flattenObject(obj: any, prefix: string = ''): any {
    const flattened: any = {};
    
    Object.keys(obj).forEach(key => {
      const value = obj[key];
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        Object.assign(flattened, this.flattenObject(value, newKey));
      } else {
        flattened[newKey] = value;
      }
    });
    
    return flattened;
  }
}
